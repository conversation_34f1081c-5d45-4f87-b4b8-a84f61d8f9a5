import 'package:flutter/foundation.dart';

class AppConfig {
  // Production URLs
  static String get _productionBaseUrl => 'https://smart-parttime.africa';
  static String get _productionApiUrl => '$_productionBaseUrl/api';
  static String get _productionSocketUrl => _productionBaseUrl;

  // Base URLs for physical device (development)
  static String get _physicalBaseUrl => 'http://192.168.1.195:3000';
  static String get _physicalApiUrl => '$_physicalBaseUrl/api';
  static String get _physicalSocketUrl => _physicalBaseUrl;

  // For Android emulator support (development)
  static String get _emulatorBaseUrl => 'http://10.0.2.2:3000';
  static String get _emulatorApiUrl => '$_emulatorBaseUrl/api';
  static String get _emulatorSocketUrl => _emulatorBaseUrl;

  // Auto-detect and return appropriate URLs
  static String get baseUrl => kReleaseMode ? _productionBaseUrl : (_isEmulator ? _emulatorBaseUrl : _physicalBaseUrl);
  static String get apiUrl => kReleaseMode ? _productionApiUrl : (_isEmulator ? _emulatorApiUrl : _physicalApiUrl);
  static String get socketUrl => kReleaseMode ? _productionSocketUrl : (_isEmulator ? _emulatorSocketUrl : _physicalSocketUrl);

  // Helper method to detect if running on emulator
  static bool get _isEmulator {
    if (kDebugMode) {
      // In debug mode, we can use platform detection
      // Simple approach...can modify this based on needs
      return false; // Default to physical device URL
    }
    return false; // Default to physical device URL
  }

  // Other configuration variables here
}
