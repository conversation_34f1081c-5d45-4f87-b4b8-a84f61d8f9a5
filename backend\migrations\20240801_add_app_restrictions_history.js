require('dotenv').config();
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

async function up() {
  try {
    logger.info('Starting migration to add app_restrictions_history table');

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS app_restrictions_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        child_app_id INT NOT NULL,
        parent_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        previous_value TEXT,
        new_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_child_app FOREIGN KEY (child_app_id) REFERENCES child_apps(id) ON DELETE CASCADE,
        CONSTRAINT fk_parent_restriction FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    logger.info('Migration to add app_restrictions_history table completed successfully');
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Starting rollback of app_restrictions_history table migration');

    await sequelize.query(`DROP TABLE IF EXISTS app_restrictions_history;`);

    logger.info('Rollback completed successfully');
  } catch (error) {
    logger.error(`Rollback failed: ${error.message}`);
    throw error;
  }
}

module.exports = { up, down };