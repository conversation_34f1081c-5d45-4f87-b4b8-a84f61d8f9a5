require('dotenv').config();
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

async function up() {
  try {
    logger.info('Starting migration to add registration code fields to users table');

    // Add registration_code column
    await sequelize.query(`
      ALTER TABLE users
      ADD COLUMN registration_code VARCHAR(20) UNIQUE;
    `);

    // Add registration_code_expires_at column
    await sequelize.query(`
      ALTER TABLE users
      ADD COLUMN registration_code_expires_at TIMESTAMP;
    `);

    // Add registration_code_used column
    await sequelize.query(`
      ALTER TABLE users
      ADD COLUMN registration_code_used BOOLEAN DEFAULT FALSE;
    `);

    // Add index for faster lookups
    await sequelize.query(`
      CREATE INDEX idx_users_registration_code
      ON users(registration_code);
    `);

    logger.info('Migration to add registration code fields completed successfully');
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Starting rollback of registration code fields migration');

    // Drop index first
    await sequelize.query(`
      DROP INDEX IF EXISTS idx_users_registration_code;
    `);

    // Drop columns
    await sequelize.query(`
      ALTER TABLE users
      DROP COLUMN IF EXISTS registration_code_used,
      DROP COLUMN IF EXISTS registration_code_expires_at,
      DROP COLUMN IF EXISTS registration_code;
    `);

    logger.info('Rollback completed successfully');
  } catch (error) {
    logger.error(`Rollback failed: ${error.message}`);
    throw error;
  }
}

module.exports = { up, down };
