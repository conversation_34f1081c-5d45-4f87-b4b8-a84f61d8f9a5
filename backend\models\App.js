const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const App = sequelize.define('App', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  package_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  app_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  icon_url: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'apps',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

module.exports = { App };
