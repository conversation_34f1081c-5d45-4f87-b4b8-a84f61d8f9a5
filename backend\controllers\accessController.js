const { User } = require('../models/User');
const { GuardianRelationship } = require('../models/GuardianRelationship');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Update a user's access level (main guardian only)
exports.updateAccessLevel = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { accessLevel } = req.body;

    // Validate access level
    if (typeof accessLevel !== 'boolean') {
      return next(new AppError('Access level must be a boolean', 400));
    }

    // Check if the requesting user is a main guardian
    if (req.user.guardian_type !== 'main_guardian') {
      return next(new AppError('Only main guardians can update access levels', 403));
    }

    // Find the user to update
    const userToUpdate = await User.findByPk(userId);

    if (!userToUpdate) {
      return next(new AppError('User not found', 404));
    }

    // Check if the user is a secondary guardian
    if (userToUpdate.guardian_type !== 'secondary_guardian') {
      return next(new AppError('Can only update access level for secondary guardians', 400));
    }

    // Check if there's a guardian relationship between the main guardian and the secondary guardian
    const relationship = await GuardianRelationship.findOne({
      where: {
        main_guardian_id: req.user.id,
        secondary_guardian_id: userId
      }
    });

    if (!relationship) {
      return next(new AppError('No relationship found with this secondary guardian', 404));
    }

    // Update the user's access level (boolean value)
    userToUpdate.access_level = accessLevel;
    await userToUpdate.save();

    // Log the access level for debugging
    logger.info(`Updated access level for user ${userId} to ${accessLevel} (${accessLevel ? 'full_access' : 'read_only'})`);

    res.status(200).json({
      success: true,
      data: {
        id: userToUpdate.id,
        username: userToUpdate.username,
        email: userToUpdate.email,
        userType: userToUpdate.user_type,
        guardianType: userToUpdate.guardian_type,
        accessLevel: userToUpdate.access_level
      }
    });
  } catch (error) {
    logger.error(`Error updating access level: ${error.message}`);
    next(error);
  }
};

// Get all secondary guardians for a main guardian
exports.getSecondaryGuardians = async (req, res, next) => {
  try {
    // Check if the requesting user is a main guardian
    if (req.user.guardian_type !== 'main_guardian') {
      return next(new AppError('Only main guardians can view secondary guardians', 403));
    }

    // Find all guardian relationships for this main guardian
    const relationships = await GuardianRelationship.findAll({
      where: { main_guardian_id: req.user.id },
      include: [
        {
          model: User,
          as: 'secondaryGuardian',
          attributes: ['id', 'username', 'email', 'access_level', 'created_at']
        }
      ]
    });

    // Format the response
    const secondaryGuardians = relationships.map(rel => ({
      id: rel.secondaryGuardian.id,
      username: rel.secondaryGuardian.username,
      email: rel.secondaryGuardian.email,
      accessLevel: rel.secondaryGuardian.access_level,
      createdAt: rel.secondaryGuardian.created_at
    }));

    res.status(200).json({
      success: true,
      count: secondaryGuardians.length,
      data: secondaryGuardians
    });
  } catch (error) {
    logger.error(`Error getting secondary guardians: ${error.message}`);
    next(error);
  }
};
