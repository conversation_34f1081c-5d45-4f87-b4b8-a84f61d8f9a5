require('dotenv').config();
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

async function up() {
  try {
    logger.info('Starting initial migration');

    // Create users table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('parent', 'child')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      );
    `);

    // Create auth_tokens table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS auth_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token TEXT NOT NULL,
        device_info TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    // Create apps table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS apps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        package_name VARCHAR(255) NOT NULL UNIQUE,
        app_name VARCHAR(255) NOT NULL,
        icon_url TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create relationships table (parent-child)
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS relationships (
        id INT AUTO_INCREMENT PRIMARY KEY,
        parent_id INT NOT NULL,
        child_id INT NOT NULL,
        is_primary_parent BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_parent FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_child FOREIGN KEY (child_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT unique_relationship UNIQUE (parent_id, child_id)
      );
    `);

    // Create child_apps table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS child_apps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        child_id INT NOT NULL,
        app_id INT NOT NULL,
        usage_time INT DEFAULT 0,
        time_limit INT,
        blocked BOOLEAN DEFAULT FALSE,
        start_time TIME,
        end_time TIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_child FOREIGN KEY (child_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_app FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE,
        CONSTRAINT unique_child_app UNIQUE (child_id, app_id)
      );
    `);

    // Create usage_stats table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS usage_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        child_id INT NOT NULL,
        app_id INT NOT NULL,
        usage_time INT NOT NULL,
        date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_child FOREIGN KEY (child_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_app FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE,
        CONSTRAINT unique_usage_stat UNIQUE (child_id, app_id, date)
      );
    `);

    logger.info('Initial migration completed successfully');
  } catch (error) {
    logger.error(`Initial migration failed: ${error.message}`);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Starting rollback of initial migration');

    // Drop tables in reverse order
    await sequelize.query(`DROP TABLE IF EXISTS usage_stats;`);
    await sequelize.query(`DROP TABLE IF EXISTS child_apps;`);
    await sequelize.query(`DROP TABLE IF EXISTS relationships;`);
    await sequelize.query(`DROP TABLE IF EXISTS apps;`);
    await sequelize.query(`DROP TABLE IF EXISTS auth_tokens;`);
    await sequelize.query(`DROP TABLE IF EXISTS users;`);

    logger.info('Rollback completed successfully');
  } catch (error) {
    logger.error(`Rollback failed: ${error.message}`);
    throw error;
  }
}

module.exports = { up, down };
