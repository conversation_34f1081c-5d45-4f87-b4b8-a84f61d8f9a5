require('dotenv').config();
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

// Import all migrations
const initialMigration = require('./20240501_initial_migration');
const addGuardianTables = require('./20240601_add_guardian_tables');
const addAccessLevel = require('./20240619_add_access_level_to_users');
const addRegistrationCode = require('./20240701_add_registration_code_to_users');
const addAppRestrictionsHistory = require('./20240801_add_app_restrictions_history');

// List of migrations in order
const migrations = [
  { name: '20240501_initial_migration', migration: initialMigration },
  { name: '20240601_add_guardian_tables', migration: addGuardianTables },
  { name: '20240619_add_access_level_to_users', migration: addAccessLevel },
  { name: '20240701_add_registration_code_to_users', migration: addRegistrationCode },
  { name: '20240801_add_app_restrictions_history', migration: addAppRestrictionsHistory },
];

// Create migrations table if it doesn't exist
async function createMigrationsTable() {
  try {
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
  } catch (error) {
    logger.error(`Failed to create migrations table: ${error.message}`);
    throw error;
  }
}

// Check if a migration has been executed
async function hasMigrationRun(name) {
  try {
    const [results] = await sequelize.query(`
      SELECT * FROM migrations WHERE name = '${name}';
    `);
    return results.length > 0;
  } catch (error) {
    logger.error(`Failed to check migration status: ${error.message}`);
    throw error;
  }
}

// Record a migration as executed
async function recordMigration(name) {
  try {
    await sequelize.query(`
      INSERT INTO migrations (name) VALUES ('${name}');
    `);
  } catch (error) {
    logger.error(`Failed to record migration: ${error.message}`);
    throw error;
  }
}

// Run all pending migrations
async function runMigrations() {
  try {
    await createMigrationsTable();

    for (const { name, migration } of migrations) {
      const hasRun = await hasMigrationRun(name);

      if (!hasRun) {
        logger.info(`Running migration: ${name}`);
        await migration.up();
        await recordMigration(name);
        logger.info(`Migration ${name} completed successfully`);
      } else {
        logger.info(`Migration ${name} already executed, skipping`);
      }
    }

    logger.info('All migrations completed successfully');
  } catch (error) {
    logger.error(`Migration process failed: ${error.message}`);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run migrations
runMigrations();
