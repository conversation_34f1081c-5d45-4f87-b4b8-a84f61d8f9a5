
<PERSON><PERSON>@Timo MINGW64 ~/Desktop/parento (master)
$ curl -X GET "https://smart-parttime.africa/api/health"
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>We're sorry, but something went wrong: Web application could not be started</title>
    <style type="text/css">body{color:#222;font-family:Arial,Sans-Serif;font-size:13px;margin:0}.column{margin-left:auto;margin-right:auto;max-width:1000px;text-align:center}header{border-bottom:1px solid #e3e3e3;margin-bottom:45px}footer,header{margin-top:50px}footer{border-top:1px solid #e3e3e3;color:#7f7f7f;font-size:14px;padding:40px 0}h1{font-size:30px;margin-bottom:10px;margin-top:30px}.subtitle{font-size:20px;margin-bottom:110px;margin-top:0}#operator_info{display:none}#show_operator_info{font-size:17px;font-weight:400}.left{padding:8px;text-align:left}h3{font-size:23px;margin-bottom:10px;margin-top:30px}ul{padding-left:16px}a,li{color:#1781bf;text-decoration:none}.error,a,li{font-weight:700}.error{background:#e6f3fc;border-radius:5px;padding:7px 12px}.error.block{display:block}.bold{font-weight:700!important}pre{margin:0;overflow-x:auto;white-space:pre-wrap;word-break:break-all}dt{font-weight:700;margin-top:16px}dd{margin-left:0}.plain{color:inherit;font-weight:inherit}#content{height:800px;overflow-y:scroll}</style>
  </head>
  <body>
    <header>
      <div class="column">
        <svg width="50" height="50" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg"><path d="m731.234002 153.838666v-18.841339c0-4.417534-3.577416-7.997327-7.990382-7.997327h-6.414571c-4.417012 0-7.990383 3.580525-7.990383 7.997327v18.841339h-18.841339c-4.417534 0-7.997327 3.577416-7.997327 7.990383v6.414571c0 4.417011 3.580525 7.990382 7.997327 7.990382h18.841339v18.841339c0 4.417534 3.577416 7.997328 7.990383 7.997328h6.414571c4.417011 0 7.990382-3.580526 7.990382-7.997328v-18.841339h18.841339c4.417534 0 7.997328-3.577416 7.997328-7.990382v-6.414571c0-4.417012-3.580526-7.990383-7.997328-7.990383z" fill="#f87575" transform="matrix(.70710678 -.70710678 .70710678 .70710678 -593.80455139 424.48059756)"/></svg>
        <h1>We're sorry, but something went wrong.</h1>
        <p class="subtitle">The issue has been logged for investigation. Please try again later.</p>        
      </div>
    </header>
    <div class="column">
      <a id="show_operator_info" href="#" onclick="showOperatorInfo()">Technical details for the administrator of this website</a>
      <div id="operator_info">
        <div class="left">
          <h3>Error ID:</h3>
          <span class="error">7f9e5ffe</span>
          <h3>Details:</h3>
          <p>Web application could not be started by the Phusion Passenger(R) application server.</p>       
          <p class="bold">Please read <a href="https://www.phusionpassenger.com/library/admin/log_file/" class="plain">the Passenger log file</a> (search for the Error ID) to find the details of the error.</p>       
          <p>You can also get a detailed report to appear directly on this page, but for security reasons it is only provided if Phusion Passenger(R) is run with <i>environment</i> set to <i>development</i> and/or with the <i>friendly error pages</i> option set to <i>on</i>.</p>
          <p>For more information about configuring environment and friendly error pages, see:</p>
          <ul>
            <li><a href="https://www.phusionpassenger.com/library/config/nginx/reference/#passenger_friendly_error_pages">Nginx integration mode</a></li>
            <li><a href="https://www.phusionpassenger.com/library/config/apache/reference/#passengerfriendlyerrorpages">Apache integration mode</a></li>
            <li><a href="https://www.phusionpassenger.com/library/config/standalone/reference/#--friendly-error-pages---no-friendly-error-pages-friendly_error_pages">Standalone mode</a></li>
          </ul>
        </div>
      </div>
    </div>
    <footer>
      <!--
       You are free to modify the footer as you see fit,
       but we kindly ask of you to preserve the following
       text. Thank you.
       -->
      <div class="column">
        This website is powered by <a href="https:<wbr>//www.phusionpassenger.com"><b>Phusion Passenger(R)</b></a>&reg;, the smart application server built by <b>Phusion</b>&reg;.
      </div>
    </footer>
    <script>
      function showOperatorInfo() {
        document.getElementById('operator_info').style.display = 'block';
      }
    </script>
  </body>
</html>

Timo Kilale@Timo MINGW64 ~/Desktop/parento (master)
$ curl -X GET "https://smart-parttime.africa/api/health"
<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>404 Not Found</title>
</head><body>
<h1>Not Found</h1>
<p>The requested URL was not found on this server.</p>
<p>Additionally, a 404 Not Found
error was encountered while trying to use an ErrorDocument to handle the request.</p>
</body></html>

Timo Kilale@Timo MINGW64 ~/Desktop/parento (master)
$ curl -X GET "https://smart-parttime.africa/api"
<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">
<html><head>
<title>301 Moved Permanently</title>
</head><body>
<h1>Moved Permanently</h1>
<p>The document has moved <a href="https://smart-parttime.africa/api/">here</a>.</p>
</body></html>

Timo Kilale@Timo MINGW64 ~/Desktop/parento (master)
$ curl -X GET "https://smart-parttime.africa/api/"
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<html>
 <head>
  <title>Index of /api</title>
 </head>
 <body>
<h1>Index of /api</h1>
  <table>
   <tr><th valign="top">&nbsp;</th><th><a href="?C=N;O=D">Name</a></th><th><a href="?C=M;O=A">Last modified</a></th><th><a href="?C=S;O=A">Size</a></th><th><a href="?C=D;O=A">Description</a></th></tr>
   <tr><th colspan="5"><hr></th></tr>
<tr><td valign="top">&nbsp;</td><td><a href="/">Parent Directory</a>       </td><td>&nbsp;</td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="app.js">app.js</a>                 </td><td align="right">2025-08-03 08:03  </td><td align="right">2.5K</td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="check-users.js">check-users.js</a>         </td><td align="right">2025-08-03 08:03  </td><td align="right">480 </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="config/">config/</a>                </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="controllers/">controllers/</a>           </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="deploy.js">deploy.js</a>              </td><td align="right">2025-08-03 08:15  </td><td align="right">882 </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="ecosystem.config.js">ecosystem.config.js</a>    </td><td align="right">2025-08-03 08:15  </td><td align="right">370 </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="logs/">logs/</a>                  </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="middleware/">middleware/</a>            </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="migrations/">migrations/</a>            </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="models/">models/</a>                </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="node_modules/">node_modules/</a>          </td><td align="right">2025-08-03 08:45  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="package-lock.json">package-lock.json</a>      </td><td align="right">2025-08-03 08:03  </td><td align="right">221K</td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="package.json">package.json</a>           </td><td align="right">2025-08-03 08:03  </td><td align="right">877 </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="public/">public/</a>                </td><td align="right">2025-08-03 08:40  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="routes/">routes/</a>                </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="scripts/">scripts/</a>               </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="server.js">server.js</a>              </td><td align="right">2025-08-03 08:03  </td><td align="right">1.2K</td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="socket/">socket/</a>                </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="test-api.js">test-api.js</a>            </td><td align="right">2025-08-03 08:03  </td><td align="right">1.7K</td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="test-generate-code.js">test-generate-code.js</a>  </td><td align="right">2025-08-03 08:03  </td><td align="right">1.8K</td><td>&nbsp;</td></tr>        
<tr><td valign="top">&nbsp;</td><td><a href="tmp/">tmp/</a>                   </td><td align="right">2025-08-03 08:40  </td><td align="right">  - </td><td>&nbsp;</td></tr>
<tr><td valign="top">&nbsp;</td><td><a href="utils/">utils/</a>                 </td><td align="right">2025-08-03 08:03  </td><td align="right">  - </td><td>&nbsp;</td></tr>
   <tr><th colspan="5"><hr></th></tr>
</table>
</body></html>

Timo Kilale@Timo MINGW64 ~/Desktop/parento (master)
$