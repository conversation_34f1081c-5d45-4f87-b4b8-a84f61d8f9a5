const { Relationship } = require('../models/Relationship');
const { User } = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { Op } = require('sequelize'); // Add this import

// Get all relationships for a parent
exports.getParentRelationships = async (req, res, next) => {
  try {
    const relationships = await Relationship.findAll({
      where: { parent_id: req.user.id },
      include: [
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email', 'user_type', 'created_at']
        }
      ]
    });

    // Add access level information to each relationship
    const relationshipsWithAccess = relationships.map(rel => {
      const data = rel.toJSON();
      // Add access level from the user's access_level property in the users table
      // This is the single source of truth for access control
      // Use the boolean value directly
      data.accessLevel = req.user.access_level;

      // Log the access level for debugging
      logger.info(`User ${req.user.id} has access_level=${req.user.access_level} (${data.accessLevel})`);

      // For backward compatibility, also set is_primary_parent
      // But this is no longer used for access control
      data.is_primary_parent = false;
      return data;
    });

    res.status(200).json({
      success: true,
      count: relationshipsWithAccess.length,
      data: relationshipsWithAccess
    });
  } catch (error) {
    next(error);
  }
};

// Get all relationships for a child
exports.getChildRelationships = async (req, res, next) => {
  try {
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id },
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'username', 'email', 'user_type', 'created_at']
        }
      ]
    });

    res.status(200).json({
      success: true,
      count: relationships.length,
      data: relationships
    });
  } catch (error) {
    next(error);
  }
};

// Create relationship (parent only)
exports.createRelationship = async (req, res, next) => {
  try {
    const { childId, isPrimaryParent } = req.body;

    // Check if child exists and is a child
    const child = await User.findByPk(childId);
    if (!child) {
      return next(new AppError('Child not found', 404));
    }
    if (child.user_type !== 'child') {
      return next(new AppError('User is not a child', 400));
    }

    // Check if relationship already exists
    const existingRelationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childId
      }
    });

    if (existingRelationship) {
      return next(new AppError('Relationship already exists', 400));
    }

    // If this is a primary parent, update any existing primary parent relationships
    if (isPrimaryParent) {
      await Relationship.update(
        { is_primary_parent: false },
        {
          where: {
            child_id: childId,
            is_primary_parent: true
          }
        }
      );
    }

    // Create relationship
    const relationship = await Relationship.create({
      parent_id: req.user.id,
      child_id: childId,
      is_primary_parent: isPrimaryParent || false
    });

    // Get relationship with child data
    const relationshipWithChild = await Relationship.findByPk(relationship.id, {
      include: [
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email', 'user_type', 'created_at']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: relationshipWithChild
    });
  } catch (error) {
    next(error);
  }
};

// Update relationship (parent only)
exports.updateRelationship = async (req, res, next) => {
  try {
    const { isPrimaryParent } = req.body;

    // Check if relationship exists
    const relationship = await Relationship.findByPk(req.params.id);
    if (!relationship) {
      return next(new AppError('Relationship not found', 404));
    }

    // Check if user is the parent in the relationship
    if (req.user.id !== relationship.parent_id) {
      return next(new AppError('Not authorized to update this relationship', 403));
    }

    // If this is becoming a primary parent, update any existing primary parent relationships
    if (isPrimaryParent) {
      await Relationship.update(
        { is_primary_parent: false },
        {
          where: {
            child_id: relationship.child_id,
            is_primary_parent: true,
            id: { [Op.ne]: relationship.id }
          }
        }
      );
    }

    // Update relationship
    relationship.is_primary_parent = isPrimaryParent;
    await relationship.save();

    // Get updated relationship with child data
    const updatedRelationship = await Relationship.findByPk(relationship.id, {
      include: [
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email', 'user_type', 'created_at']
        }
      ]
    });

    res.status(200).json({
      success: true,
      data: updatedRelationship
    });
  } catch (error) {
    next(error);
  }
};

// Delete relationship
exports.deleteRelationship = async (req, res, next) => {
  try {
    // Check if relationship exists
    const relationship = await Relationship.findByPk(req.params.id);
    if (!relationship) {
      return next(new AppError('Relationship not found', 404));
    }

    // Check if user is the parent or child in the relationship
    if (req.user.id !== relationship.parent_id && req.user.id !== relationship.child_id) {
      return next(new AppError('Not authorized to delete this relationship', 403));
    }

    // Delete relationship
    await relationship.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};
