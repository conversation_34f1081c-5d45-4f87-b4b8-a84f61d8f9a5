const crypto = require('crypto');
const { ReferralCode } = require('../models/ReferralCode');
const { User } = require('../models/User');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Generate a new referral code
exports.generateReferralCode = async (req, res, next) => {
  try {
    const { accessLevel, expiresInDays = 7 } = req.body;

    // Check if user is a parent
    if (req.user.user_type !== 'parent') {
      return next(new AppError('Only parents can generate referral codes', 403));
    }

    // Set guardian type to main_guardian if not already set
    if (!req.user.guardian_type) {
      await User.update(
        { guardian_type: 'main_guardian' },
        { where: { id: req.user.id } }
      );
    } else if (req.user.guardian_type !== 'main_guardian') {
      return next(new AppError('Only main guardians can generate referral codes', 403));
    }

    // Generate a random code
    const code = crypto.randomBytes(8).toString('hex');

    // Calculate expiry date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    // Create referral code
    const referralCode = await ReferralCode.create({
      creator_id: req.user.id,
      code,
      access_level: accessLevel || 'read_only',
      expires_at: expiresAt
    });

    res.status(201).json({
      success: true,
      data: {
        id: referralCode.id,
        code: referralCode.code,
        accessLevel: referralCode.access_level,
        expiresAt: referralCode.expires_at
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get all referral codes for a user
exports.getReferralCodes = async (req, res, next) => {
  try {
    const referralCodes = await ReferralCode.findAll({
      where: { creator_id: req.user.id },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email'],
          required: false
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.status(200).json({
      success: true,
      count: referralCodes.length,
      data: referralCodes
    });
  } catch (error) {
    next(error);
  }
};

// Validate a referral code
exports.validateReferralCode = async (req, res, next) => {
  try {
    const { code } = req.params;

    const referralCode = await ReferralCode.findOne({
      where: { code },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!referralCode) {
      return next(new AppError('Invalid referral code', 404));
    }

    // Check if code is expired
    if (new Date() > new Date(referralCode.expires_at)) {
      return next(new AppError('Referral code has expired', 400));
    }

    // Check if code is already used
    if (referralCode.used_by) {
      return next(new AppError('Referral code has already been used', 400));
    }

    res.status(200).json({
      success: true,
      data: {
        id: referralCode.id,
        code: referralCode.code,
        accessLevel: referralCode.access_level,
        expiresAt: referralCode.expires_at,
        creator: referralCode.creator
      }
    });
  } catch (error) {
    next(error);
  }
};

// Delete a referral code
exports.deleteReferralCode = async (req, res, next) => {
  try {
    const { id } = req.params;

    const referralCode = await ReferralCode.findByPk(id);

    if (!referralCode) {
      return next(new AppError('Referral code not found', 404));
    }

    if (referralCode.creator_id !== req.user.id) {
      return next(new AppError('Not authorized to delete this referral code', 403));
    }

    await referralCode.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

module.exports = exports;
