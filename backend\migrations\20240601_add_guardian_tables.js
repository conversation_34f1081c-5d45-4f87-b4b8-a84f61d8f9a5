require('dotenv').config();
const { sequelize } = require('../config/database');
const { QueryTypes } = require('sequelize');
const logger = require('../utils/logger');

async function up() {
  try {
    logger.info('Starting migration: Adding guardian tables');

    // Add guardian_type column to users table
    await sequelize.query(`
      ALTER TABLE users
      ADD COLUMN guardian_type VARCHAR(20) DEFAULT NULL
      CHECK (guardian_type IN ('main_guardian', 'secondary_guardian', NULL));
    `);

    // Create referral_codes table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS referral_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        creator_id INT NOT NULL,
        code VARCHAR(20) NOT NULL UNIQUE,
        access_level VARCHAR(20) NOT NULL DEFAULT 'read_only'
          CHECK (access_level IN ('read_only', 'full_access')),
        expires_at TIMESTAMP NOT NULL,
        used_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_creator <PERSON>OR<PERSON><PERSON><PERSON> (creator_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_used_by FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL
      );
    `);

    // Create guardian_relationships table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS guardian_relationships (
        id INT AUTO_INCREMENT PRIMARY KEY,
        main_guardian_id INT NOT NULL,
        secondary_guardian_id INT NOT NULL,
        access_level VARCHAR(20) NOT NULL DEFAULT 'read_only'
          CHECK (access_level IN ('read_only', 'full_access')),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_main_guardian FOREIGN KEY (main_guardian_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT fk_secondary_guardian FOREIGN KEY (secondary_guardian_id) REFERENCES users(id) ON DELETE CASCADE,
        CONSTRAINT unique_guardian_relationship UNIQUE (main_guardian_id, secondary_guardian_id)
      );
    `);
    // Update existing parent users to be main_guardians
    await sequelize.query(`
      UPDATE users
      SET guardian_type = 'main_guardian'
      WHERE user_type = 'parent' AND guardian_type IS NULL;
    `);

    logger.info('Migration completed successfully');
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Starting rollback: Removing guardian tables');

    // Drop guardian_relationships table
    await sequelize.query(`DROP TABLE IF EXISTS guardian_relationships;`);

    // Drop referral_codes table
    await sequelize.query(`DROP TABLE IF EXISTS referral_codes;`);

    // Remove guardian_type column from users table
    await sequelize.query(`
      ALTER TABLE users
      DROP COLUMN IF EXISTS guardian_type;
    `);

    logger.info('Rollback completed successfully');
  } catch (error) {
    logger.error(`Rollback failed: ${error.message}`);
    throw error;
  }
}

module.exports = { up, down };
