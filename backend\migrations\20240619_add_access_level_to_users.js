const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

async function up() {
  try {
    // Add access_level column to users table
    await sequelize.query(`
      ALTER TABLE users 
      ADD COLUMN IF NOT EXISTS access_level BOOLEAN DEFAULT FALSE;
    `);

    // Set access_level=true for all users with user_type='parent' and guardian_type='main_guardian'
    await sequelize.query(`
      UPDATE users 
      SET access_level = TRUE 
      WHERE user_type = 'parent' AND guardian_type = 'main_guardian';
    `);

    logger.info('Added access_level column to users table');
  } catch (error) {
    logger.error('Error adding access_level column to users table:', error);
    throw error;
  }
}

async function down() {
  try {
    // Remove access_level column from users table
    await sequelize.query(`
      ALTER TABLE users 
      DROP COLUMN IF EXISTS access_level;
    `);

    logger.info('Removed access_level column from users table');
  } catch (error) {
    logger.error('Error removing access_level column from users table:', error);
    throw error;
  }
}

module.exports = { up, down };
