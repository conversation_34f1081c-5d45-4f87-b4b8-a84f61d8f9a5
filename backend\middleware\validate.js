const { validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

/**
 * Middleware to validate request data using express-validator
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.validate = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    // Get the first error message
    const errorMessage = errors.array()[0].msg;
    
    // Return a 400 Bad Request with the error message
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};
