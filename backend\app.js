const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { errorHandler } = require('./middleware/errorHandler');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const appRoutes = require('./routes/apps');
const childAppRoutes = require('./routes/childApps');
const usageStatsRoutes = require('./routes/usageStats');
const relationshipRoutes = require('./routes/relationships');
const referralCodeRoutes = require('./routes/referralCodes');
const guardianRelationshipRoutes = require('./routes/guardianRelationships');
const appUsageRoutes = require('./routes/appUsage');
const accessRoutes = require('./routes/access');
const healthRouter = require('./routes/health');

const app = express();

// Apply security middleware
app.use(helmet());
app.use(cors({
  origin: '*',  // Warning: Don't use in production  origin: [
 //   'http://localhost:3000',
 //   'http://localhost:53325',
 //   'http://************:3000',
 // ],

  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'x-app-type'  // Added this header to fix CORS issue
  ],
  credentials: true
}));

// Request logging
app.use(morgan('combined'));

// Parse JSON request body
app.use(express.json());

// Apply rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(apiLimiter);

// API routes (without /api prefix since web server strips it)
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/apps', appRoutes);
app.use('/child-apps', childAppRoutes);
app.use('/usage-stats', usageStatsRoutes);
app.use('/relationships', relationshipRoutes);
app.use('/referral-codes', referralCodeRoutes);
app.use('/guardian-relationships', guardianRelationshipRoutes);
app.use('/app-usage', appUsageRoutes);
app.use('/access', accessRoutes);
const websiteRoutes = require('./routes/websites');
app.use('/websites', websiteRoutes);

// Health check endpoint
app.use('/health', healthRouter);

// Error handling middleware
app.use(errorHandler);

module.exports = app;




