const fs = require('fs');
const path = require('path');

console.log('🚀 Preparing for deployment...');

// Copy production environment file
if (fs.existsSync('.env.production')) {
  fs.copyFileSync('.env.production', '.env');
  console.log('✅ Production environment file copied');
} else {
  console.log('❌ .env.production file not found');
}

// Check if all required files exist
const requiredFiles = [
  'package.json',
  'server.js',
  'app.js',
  '.env'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('🎉 All required files are present. Ready for deployment!');
} else {
  console.log('⚠️  Some required files are missing. Please check before deploying.');
}
