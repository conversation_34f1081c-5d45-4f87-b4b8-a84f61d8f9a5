const { GuardianRelationship } = require('../models/GuardianRelationship');
const { User } = require('../models/User');
const { Relationship } = require('../models/Relationship');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Get all secondary guardians for a main guardian
exports.getSecondaryGuardians = async (req, res, next) => {
  try {
    const relationships = await GuardianRelationship.findAll({
      where: { main_guardian_id: req.user.id },
      include: [
        {
          model: User,
          as: 'secondaryGuardian',
          attributes: ['id', 'username', 'email', 'created_at']
        }
      ]
    });

    res.status(200).json({
      success: true,
      count: relationships.length,
      data: relationships
    });
  } catch (error) {
    next(error);
  }
};

// Get all main guardians for a secondary guardian
exports.getMainGuardians = async (req, res, next) => {
  try {
    const relationships = await GuardianRelationship.findAll({
      where: { secondary_guardian_id: req.user.id },
      include: [
        {
          model: User,
          as: 'mainGuardian',
          attributes: ['id', 'username', 'email', 'created_at']
        }
      ]
    });

    res.status(200).json({
      success: true,
      count: relationships.length,
      data: relationships
    });
  } catch (error) {
    next(error);
  }
};

// Update guardian relationship access level
exports.updateAccessLevel = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { accessLevel } = req.body;

    // Validate access level
    if (typeof accessLevel !== 'boolean') {
      return next(new AppError('Access level must be a boolean', 400));
    }

    // Find the relationship
    const relationship = await GuardianRelationship.findByPk(id);

    if (!relationship) {
      return next(new AppError('Guardian relationship not found', 404));
    }

    // Check if user is the main guardian
    if (relationship.main_guardian_id !== req.user.id) {
      return next(new AppError('Not authorized to update this relationship', 403));
    }

    // Update access level
    relationship.access_level = accessLevel;
    await relationship.save();

    res.status(200).json({
      success: true,
      data: relationship
    });
  } catch (error) {
    next(error);
  }
};

// Remove a guardian relationship
exports.removeGuardianRelationship = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find the relationship
    const relationship = await GuardianRelationship.findByPk(id);

    if (!relationship) {
      return next(new AppError('Guardian relationship not found', 404));
    }

    // Check if user is the main guardian or the secondary guardian
    if (relationship.main_guardian_id !== req.user.id &&
        relationship.secondary_guardian_id !== req.user.id) {
      return next(new AppError('Not authorized to remove this relationship', 403));
    }

    // Delete the relationship
    await relationship.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// Get children accessible to a secondary guardian
exports.getAccessibleChildren = async (req, res, next) => {
  try {
    logger.info(`Getting accessible children for secondary guardian ID: ${req.user.id}`);

    // Find all main guardians for this secondary guardian
    const guardianRelationships = await GuardianRelationship.findAll({
      where: { secondary_guardian_id: req.user.id },
      include: [{
        model: User,
        as: 'mainGuardian',
        attributes: ['id', 'username', 'email']
      }],
      attributes: ['id', 'main_guardian_id', 'access_level']
    });

    logger.info(`Found ${guardianRelationships.length} guardian relationships`);

    if (guardianRelationships.length === 0) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // Get all main guardian IDs
    const mainGuardianIds = guardianRelationships.map(rel => rel.main_guardian_id);
    logger.info(`Main guardian IDs: ${mainGuardianIds.join(', ')}`);

    // Get access levels for each main guardian
    const accessLevels = {};
    guardianRelationships.forEach(rel => {
      accessLevels[rel.main_guardian_id] = rel.access_level;
    });

    // Find all children for these main guardians
    const parentChildRelationships = await Relationship.findAll({
      where: { parent_id: mainGuardianIds },
      include: [
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email', 'created_at']
        },
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    logger.info(`Found ${parentChildRelationships.length} parent-child relationships`);

    // Debug the relationships
    parentChildRelationships.forEach((rel, index) => {
      logger.info(`Relationship ${index + 1}: parent_id=${rel.parent_id}, child_id=${rel.child_id}, ` +
                 `child=${rel.child ? rel.child.username : 'null'}`);
    });

    // Add access level to each relationship
    const relationshipsWithAccess = parentChildRelationships.map(rel => {
      // Convert to plain object
      const data = rel.toJSON();
      // Add access level from the user's access_level property in the users table
      // This is the single source of truth for access control
      // Use the boolean value directly
      data.accessLevel = req.user.access_level;

      // Log the access level for debugging
      logger.info(`User ${req.user.id} has access_level=${req.user.access_level} (${data.accessLevel})`);

      // For backward compatibility, also set is_primary_parent based on access_level
      // But this is no longer used for access control
      data.is_primary_parent = false;
      return data;
    });

    res.status(200).json({
      success: true,
      count: relationshipsWithAccess.length,
      data: relationshipsWithAccess
    });
  } catch (error) {
    logger.error(`Error getting accessible children: ${error.message}`);
    logger.error(error.stack);
    next(error);
  }
};

module.exports = exports;
