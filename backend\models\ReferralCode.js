const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');

const ReferralCode = sequelize.define('ReferralCode', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  creator_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true
  },
  access_level: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'read_only',
    validate: {
      isIn: [['read_only', 'full_access']]
    }
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false
  },
  used_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'referral_codes',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

// Define associations
ReferralCode.belongsTo(User, { foreignKey: 'creator_id', as: 'creator' });
ReferralCode.belongsTo(User, { foreignKey: 'used_by', as: 'user' });

// Add reverse associations to User model
User.hasMany(ReferralCode, { foreignKey: 'creator_id', as: 'createdReferralCodes' });
User.hasMany(ReferralCode, { foreignKey: 'used_by', as: 'usedReferralCodes' });

module.exports = { ReferralCode };
