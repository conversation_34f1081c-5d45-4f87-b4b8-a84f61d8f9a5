const { Website } = require('../models/Website');
const { User } = require('../models/User');
const { asyncHandler } = require('../middleware/asyncHandler');
const AppError = require('../utils/appError');

// Get all blocked websites for a child
const getChildWebsites = asyncHandler(async (req, res) => {
  const { childId } = req.params;

  // Verify child exists and user has access
  const child = await User.findByPk(childId);
  if (!child) {
    throw new AppError('Child not found', 404);
  }

  const websites = await Website.findAll({
    where: { child_id: childId },
    order: [['domain', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: websites
  });
});

// Get blocked websites for current child (child only)
const getBlockedWebsites = asyncHandler(async (req, res) => {
  const websites = await Website.findAll({
    where: { 
      child_id: req.user.id,
      blocked: true 
    },
    order: [['domain', 'ASC']]
  });

  res.status(200).json({
    status: 'success',
    data: websites
  });
});

// Add blocked website
const addBlockedWebsite = asyncHandler(async (req, res) => {
  const { childId, domain } = req.body;

  // Verify child exists
  const child = await User.findByPk(childId);
  if (!child) {
    throw new AppError('Child not found', 404);
  }

  // Clean domain
  const cleanDomain = domain.toLowerCase()
    .replace(/^https?:\/\//, '')
    .replace(/^www\./, '')
    .split('/')[0];

  const website = await Website.create({
    child_id: childId,
    domain: cleanDomain,
    blocked: true
  });

  res.status(201).json({
    status: 'success',
    data: website
  });
});

// Update website block status
const updateWebsiteBlockStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { blocked } = req.body;

  const website = await Website.findByPk(id);
  if (!website) {
    throw new AppError('Website not found', 404);
  }

  website.blocked = blocked;
  await website.save();

  res.status(200).json({
    status: 'success',
    data: website
  });
});

// Remove blocked website
const removeBlockedWebsite = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const website = await Website.findByPk(id);
  if (!website) {
    throw new AppError('Website not found', 404);
  }

  await website.destroy();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Batch update websites
const batchUpdateWebsites = asyncHandler(async (req, res) => {
  const { childId } = req.params;
  const { updates } = req.body;

  const results = [];
  
  for (const update of updates) {
    if (update.action === 'add') {
      const cleanDomain = update.domain.toLowerCase()
        .replace(/^https?:\/\//, '')
        .replace(/^www\./, '')
        .split('/')[0];
        
      const website = await Website.create({
        child_id: childId,
        domain: cleanDomain,
        blocked: true
      });
      results.push(website);
    } else if (update.action === 'remove') {
      await Website.destroy({
        where: {
          child_id: childId,
          domain: update.domain
        }
      });
    }
  }

  res.status(200).json({
    status: 'success',
    data: results
  });
});

module.exports = {
  getChildWebsites,
  getBlockedWebsites,
  addBlockedWebsite,
  updateWebsiteBlockStatus,
  removeBlockedWebsite,
  batchUpdateWebsites
};