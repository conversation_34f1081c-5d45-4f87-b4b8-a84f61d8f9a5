const { ChildApp } = require('../models/ChildApp');
const { App } = require('../models/App');
const { User } = require('../models/User');
const { Relationship } = require('../models/Relationship');
const { AppRestrictionHistory } = require('../models/AppRestrictionHistory');
const { AppError } = require('../middleware/errorHandler');
const { getIo } = require('../socket');
const logger = require('../utils/logger');

// Get all child apps for a child
exports.getChildApps = async (req, res, next) => {
  try {
    const { childId } = req.params;
    logger.info(`Getting child apps for child ID: ${childId} by user ID: ${req.user.id}`);

    // Check if user is authorized to access child's apps
    if (req.user.id !== parseInt(childId)) {
      // If parent, check relationship
      if (req.user.user_type === 'parent') {
        // Check if user is a direct parent
        const relationship = await Relationship.findOne({
          where: {
            parent_id: req.user.id,
            child_id: childId
          }
        });

        // If direct relationship exists, allow access
        if (relationship) {
          logger.info(`User ${req.user.id} has direct relationship with child ${childId}`);
        }
        // If no direct relationship, check if user is a secondary guardian
        else if (req.user.guardian_type === 'secondary_guardian') {
          logger.info(`User ${req.user.id} is a secondary guardian, checking access rights`);

          // Find all main guardians for this secondary guardian
          const { GuardianRelationship } = require('../models/GuardianRelationship');
          const guardianRelationships = await GuardianRelationship.findAll({
            where: { secondary_guardian_id: req.user.id },
            attributes: ['main_guardian_id']
          });

          // Get all main guardian IDs
          const mainGuardianIds = guardianRelationships.map(rel => rel.main_guardian_id);
          logger.info(`Main guardian IDs: ${mainGuardianIds.join(', ')}`);

          // Check if any of the main guardians have a relationship with this child
          const parentChildRelationship = await Relationship.findOne({
            where: {
              parent_id: mainGuardianIds,
              child_id: childId
            }
          });

          if (!parentChildRelationship) {
            logger.info(`No relationship found between main guardians and child ${childId}`);
            return next(new AppError('Not authorized to access this child\'s apps', 403));
          }

          logger.info(`Found relationship between main guardian ${parentChildRelationship.parent_id} and child ${childId}`);

          // Check the user's access level directly from the users table
          logger.info(`Secondary guardian has access_level=${req.user.access_level}`);

          // No need to check guardian relationship access level anymore
        } else {
          logger.info(`User ${req.user.id} has no relationship with child ${childId}`);
          return next(new AppError('Not authorized to access this child\'s apps', 403));
        }
      } else {
        logger.info(`User ${req.user.id} is not a parent`);
        return next(new AppError('Not authorized to access this child\'s apps', 403));
      }
    }

    // Get child apps
    const childApps = await ChildApp.findAll({
      where: { child_id: childId },
      include: [
        {
          model: App,
          as: 'app'
        }
      ]
    });

    // Format response
    const formattedApps = childApps.map(childApp => ({
      id: childApp.id,
      appId: childApp.app_id,
      packageName: childApp.app.package_name,
      appName: childApp.app.app_name,
      iconUrl: childApp.app.icon_url,
      usageTime: childApp.usage_time,
      timeLimit: childApp.time_limit,
      blocked: childApp.blocked,
      startTime: childApp.start_time,
      endTime: childApp.end_time
    }));

    res.status(200).json({
      success: true,
      count: formattedApps.length,
      data: formattedApps
    });
  } catch (error) {
    next(error);
  }
};

// Get blocked apps for the current child
exports.getBlockedApps = async (req, res, next) => {
  try {
    // Check if user is a child
    if (req.user.user_type !== 'child') {
      return next(new AppError('Only child accounts can access this endpoint', 403));
    }

    // Get all child apps for the current child
    const childApps = await ChildApp.findAll({
      where: {
        child_id: req.user.id
      },
      include: [
        {
          model: App,
          as: 'app'
        }
      ]
    });

    // Format response
    const formattedApps = childApps.map(childApp => ({
      id: childApp.id,
      appId: childApp.app_id,
      packageName: childApp.app.package_name,
      appName: childApp.app.app_name,
      blocked: childApp.blocked,
      timeLimit: childApp.time_limit,
      startTime: childApp.start_time,
      endTime: childApp.end_time,
      usageTime: childApp.usage_time
    }));

    res.status(200).json({
      success: true,
      count: formattedApps.length,
      data: formattedApps
    });
  } catch (error) {
    logger.error(`Error getting blocked apps: ${error.message}`);
    next(error);
  }
};

// Get child app by ID
exports.getChildAppById = async (req, res, next) => {
  try {
    const childApp = await ChildApp.findByPk(req.params.id, {
      include: [
        {
          model: App,
          as: 'app'
        },
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!childApp) {
      return next(new AppError('Child app not found', 404));
    }

    // Check if user is authorized to access this child app
    if (req.user.id !== childApp.child_id) {
      // If parent, check relationship
      if (req.user.user_type === 'parent') {
        // Check if user is a direct parent
        const relationship = await Relationship.findOne({
          where: {
            parent_id: req.user.id,
            child_id: childApp.child_id
          }
        });

        // If direct relationship exists, allow access
        if (relationship) {
          logger.info(`User ${req.user.id} has direct relationship with child ${childApp.child_id}`);
        }
        // If no direct relationship, check if user is a secondary guardian
        else if (req.user.guardian_type === 'secondary_guardian') {
          logger.info(`User ${req.user.id} is a secondary guardian, checking access rights`);

          // Find all main guardians for this secondary guardian
          const { GuardianRelationship } = require('../models/GuardianRelationship');
          const guardianRelationships = await GuardianRelationship.findAll({
            where: { secondary_guardian_id: req.user.id },
            attributes: ['main_guardian_id', 'access_level']
          });

          // Get all main guardian IDs
          const mainGuardianIds = guardianRelationships.map(rel => rel.main_guardian_id);

          // Check if any of the main guardians have a relationship with this child
          const parentChildRelationship = await Relationship.findOne({
            where: {
              parent_id: mainGuardianIds,
              child_id: childApp.child_id
            }
          });

          if (!parentChildRelationship) {
            return next(new AppError('Not authorized to access this child app', 403));
          }

          // Find the access level for this secondary guardian
          const guardianRelationship = guardianRelationships.find(
            rel => rel.main_guardian_id === parentChildRelationship.parent_id
          );

          if (!guardianRelationship) {
            return next(new AppError('Not authorized to access this child app', 403));
          }

          logger.info(`Secondary guardian has ${guardianRelationship.access_level} access`);
        } else {
          return next(new AppError('Not authorized to access this child app', 403));
        }
      } else {
        return next(new AppError('Not authorized to access this child app', 403));
      }
    }

    // Format response
    const formattedApp = {
      id: childApp.id,
      appId: childApp.app_id,
      childId: childApp.child_id,
      packageName: childApp.app.package_name,
      appName: childApp.app.app_name,
      iconUrl: childApp.app.icon_url,
      usageTime: childApp.usage_time,
      timeLimit: childApp.time_limit,
      blocked: childApp.blocked,
      startTime: childApp.start_time,
      endTime: childApp.end_time,
      child: childApp.child
    };

    res.status(200).json({
      success: true,
      data: formattedApp
    });
  } catch (error) {
    next(error);
  }
};

// Update child app (parent only)
exports.updateChildApp = async (req, res, next) => {
  try {
    const { timeLimit, blocked, startTime, endTime } = req.body;

    // Get child app
    const childApp = await ChildApp.findByPk(req.params.id, {
      include: [
        {
          model: App,
          as: 'app'
        }
      ]
    });

    if (!childApp) {
      return next(new AppError('Child app not found', 404));
    }

    // Check if parent has relationship with child
    let canUpdate = false;
    let isSecondaryGuardian = false;

    // Check if user is a direct parent
    const relationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childApp.child_id
      }
    });

    if (relationship) {
      logger.info(`User ${req.user.id} has direct relationship with child ${childApp.child_id}`);
      canUpdate = true;
    }
    // If no direct relationship, check if user is a secondary guardian
    else if (req.user.guardian_type === 'secondary_guardian') {
      logger.info(`User ${req.user.id} is a secondary guardian, checking access rights`);
      isSecondaryGuardian = true;

      // Find all main guardians for this secondary guardian
      const { GuardianRelationship } = require('../models/GuardianRelationship');
      const guardianRelationships = await GuardianRelationship.findAll({
        where: { secondary_guardian_id: req.user.id },
        attributes: ['main_guardian_id', 'access_level']
      });

      // Get all main guardian IDs
      const mainGuardianIds = guardianRelationships.map(rel => rel.main_guardian_id);

      // Check if any of the main guardians have a relationship with this child
      const parentChildRelationship = await Relationship.findOne({
        where: {
          parent_id: mainGuardianIds,
          child_id: childApp.child_id
        }
      });

      if (parentChildRelationship) {
        // Find the access level for this secondary guardian
        const guardianRelationship = guardianRelationships.find(
          rel => rel.main_guardian_id === parentChildRelationship.parent_id
        );

        // Check the user's access level directly from the users table
        if (req.user.access_level) {
          logger.info(`Secondary guardian has full access (access_level=true), allowing update`);
          canUpdate = true;
        } else {
          logger.info(`Secondary guardian has read-only access (access_level=false), denying update`);
        }
      }
    }

    if (!canUpdate) {
      return next(new AppError('Not authorized to update this child app', 403));
    }

    // Store previous values for history
    const previousValues = {
      timeLimit: childApp.time_limit,
      blocked: childApp.blocked,
      startTime: childApp.start_time,
      endTime: childApp.end_time
    };

    // Update child app
    if (timeLimit !== undefined) childApp.time_limit = timeLimit;
    if (blocked !== undefined) childApp.blocked = blocked;
    if (startTime !== undefined) childApp.start_time = startTime;
    if (endTime !== undefined) childApp.end_time = endTime;

    await childApp.save();

    // Create restriction history
    await AppRestrictionHistory.create({
      child_app_id: childApp.id,
      parent_id: req.user.id,
      action: 'update',
      previous_value: JSON.stringify(previousValues),
      new_value: JSON.stringify({
        timeLimit: childApp.time_limit,
        blocked: childApp.blocked,
        startTime: childApp.start_time,
        endTime: childApp.end_time
      })
    });

    // Notify child via WebSocket
    const io = getIo();
    io.to(`child:${childApp.child_id}`).emit('restriction:updated', {
      appId: childApp.app_id,
      packageName: childApp.app.package_name,
      updates: {
        timeLimit: childApp.time_limit,
        blocked: childApp.blocked,
        startTime: childApp.start_time,
        endTime: childApp.end_time
      }
    });

    res.status(200).json({
      success: true,
      data: {
        id: childApp.id,
        appId: childApp.app_id,
        childId: childApp.child_id,
        packageName: childApp.app.package_name,
        appName: childApp.app.app_name,
        usageTime: childApp.usage_time,
        timeLimit: childApp.time_limit,
        blocked: childApp.blocked,
        startTime: childApp.start_time,
        endTime: childApp.end_time
      }
    });
  } catch (error) {
    next(error);
  }
};

// Batch update child apps (parent only)
exports.batchUpdateChildApps = async (req, res, next) => {
  try {
    const { childId } = req.params;
    const { updates } = req.body;

    // Check if parent has relationship with child
    let canUpdate = false;
    let isSecondaryGuardian = false;

    // Check if user is a direct parent
    const relationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childId
      }
    });

    if (relationship) {
      logger.info(`User ${req.user.id} has direct relationship with child ${childId}`);
      canUpdate = true;
    }
    // If no direct relationship, check if user is a secondary guardian
    else if (req.user.guardian_type === 'secondary_guardian') {
      logger.info(`User ${req.user.id} is a secondary guardian, checking access rights`);
      isSecondaryGuardian = true;

      // Find all main guardians for this secondary guardian
      const { GuardianRelationship } = require('../models/GuardianRelationship');
      const guardianRelationships = await GuardianRelationship.findAll({
        where: { secondary_guardian_id: req.user.id },
        attributes: ['main_guardian_id', 'access_level']
      });

      // Get all main guardian IDs
      const mainGuardianIds = guardianRelationships.map(rel => rel.main_guardian_id);

      // Check if any of the main guardians have a relationship with this child
      const parentChildRelationship = await Relationship.findOne({
        where: {
          parent_id: mainGuardianIds,
          child_id: childId
        }
      });

      if (parentChildRelationship) {
        // Find the access level for this secondary guardian
        const guardianRelationship = guardianRelationships.find(
          rel => rel.main_guardian_id === parentChildRelationship.parent_id
        );

        // Check the user's access level directly from the users table
        if (req.user.access_level) {
          logger.info(`Secondary guardian has full access (access_level=true), allowing batch update`);
          canUpdate = true;
        } else {
          logger.info(`Secondary guardian has read-only access (access_level=false), denying batch update`);
        }
      }
    }

    if (!canUpdate) {
      return next(new AppError('Not authorized to update this child\'s apps', 403));
    }

    // Process each update
    const updatedApps = [];
    const notificationsData = [];

    for (const update of updates) {
      const { appId, timeLimit, blocked, startTime, endTime } = update;

      // Get child app
      const childApp = await ChildApp.findOne({
        where: {
          child_id: childId,
          app_id: appId
        },
        include: [
          {
            model: App,
            as: 'app'
          }
        ]
      });

      if (!childApp) {
        continue;
      }

      // Store previous values for history
      const previousValues = {
        timeLimit: childApp.time_limit,
        blocked: childApp.blocked,
        startTime: childApp.start_time,
        endTime: childApp.end_time
      };

      // Update child app
      if (timeLimit !== undefined) childApp.time_limit = timeLimit;
      if (blocked !== undefined) childApp.blocked = blocked;
      if (startTime !== undefined) childApp.start_time = startTime;
      if (endTime !== undefined) childApp.end_time = endTime;

      await childApp.save();

      // Create restriction history
      await AppRestrictionHistory.create({
        child_app_id: childApp.id,
        parent_id: req.user.id,
        action: 'update',
        previous_value: JSON.stringify(previousValues),
        new_value: JSON.stringify({
          timeLimit: childApp.time_limit,
          blocked: childApp.blocked,
          startTime: childApp.start_time,
          endTime: childApp.end_time
        })
      });

      // Add to updated apps
      updatedApps.push({
        id: childApp.id,
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        appName: childApp.app.app_name,
        usageTime: childApp.usage_time,
        timeLimit: childApp.time_limit,
        blocked: childApp.blocked,
        startTime: childApp.start_time,
        endTime: childApp.end_time
      });

      // Add to notifications data
      notificationsData.push({
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        updates: {
          timeLimit: childApp.time_limit,
          blocked: childApp.blocked,
          startTime: childApp.start_time,
          endTime: childApp.end_time
        }
      });
    }

    // Notify child via WebSocket
    const io = getIo();
    io.to(`child:${childId}`).emit('restriction:batch-updated', {
      updates: notificationsData
    });

    res.status(200).json({
      success: true,
      count: updatedApps.length,
      data: updatedApps
    });
  } catch (error) {
    next(error);
  }
};

// Update child app usage time (child only)
exports.updateUsageTime = async (req, res, next) => {
  try {
    const { usageTime } = req.body;

    // Get child app
    const childApp = await ChildApp.findByPk(req.params.id, {
      include: [
        {
          model: App,
          as: 'app'
        }
      ]
    });

    if (!childApp) {
      return next(new AppError('Child app not found', 404));
    }

    // Check if user is the child
    if (req.user.id !== childApp.child_id) {
      return next(new AppError('Not authorized to update this child app', 403));
    }

    // Update usage time
    childApp.usage_time = usageTime;
    await childApp.save();

    // Get parents of this child
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id }
    });

    // Notify parents via WebSocket
    const io = getIo();
    for (const relationship of relationships) {
      io.to(`user:${relationship.parent_id}`).emit('usage:updated', {
        childId: req.user.id,
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        usageTime
      });
    }

    res.status(200).json({
      success: true,
      data: {
        id: childApp.id,
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        appName: childApp.app.app_name,
        usageTime: childApp.usage_time
      }
    });
  } catch (error) {
    next(error);
  }
};

// Batch update usage time (child only)
exports.batchUpdateUsageTime = async (req, res, next) => {
  try {
    const { updates } = req.body;

    // Process each update
    const updatedApps = [];
    const notificationsData = [];

    for (const update of updates) {
      const { appId, usageTime } = update;

      // Get child app
      const childApp = await ChildApp.findOne({
        where: {
          child_id: req.user.id,
          app_id: appId
        },
        include: [
          {
            model: App,
            as: 'app'
          }
        ]
      });

      if (!childApp) {
        continue;
      }

      // Update usage time
      childApp.usage_time = usageTime;
      await childApp.save();

      // Add to updated apps
      updatedApps.push({
        id: childApp.id,
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        appName: childApp.app.app_name,
        usageTime: childApp.usage_time
      });

      // Add to notifications data
      notificationsData.push({
        appId: childApp.app_id,
        packageName: childApp.app.package_name,
        usageTime
      });
    }

    // Get parents of this child
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id }
    });

    // Notify parents via WebSocket
    const io = getIo();
    for (const relationship of relationships) {
      io.to(`user:${relationship.parent_id}`).emit('usage:batch-updated', {
        childId: req.user.id,
        updates: notificationsData
      });
    }

    res.status(200).json({
      success: true,
      count: updatedApps.length,
      data: updatedApps
    });
  } catch (error) {
    next(error);
  }
};
