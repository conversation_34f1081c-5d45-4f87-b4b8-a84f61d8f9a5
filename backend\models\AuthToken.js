const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');

const AuthToken = sequelize.define('AuthToken', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  token: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  device_info: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'auth_tokens',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

// Define associations
AuthToken.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
User.hasMany(AuthToken, { foreignKey: 'user_id', as: 'tokens' });

module.exports = { AuthToken };
