{"version": 3, "names": ["_semver", "require", "_plugins", "_utils", "targetsSupported", "target", "support", "targetEnvironments", "Object", "keys", "length", "unsupportedEnvironments", "filter", "environment", "lowestImplementedVersion", "getLowestImplementedVersion", "lowestTargetedVersion", "isUnreleasedVersion", "semver", "valid", "toString", "Error", "gt", "semverify", "isRequired", "name", "targets", "compatData", "pluginsCompatData", "includes", "excludes", "has", "filterItems", "list", "defaultIncludes", "defaultExcludes", "pluginSyntaxMap", "result", "Set", "options", "item", "add", "shippedProposalsSyntax", "get", "for<PERSON>ach", "delete"], "sources": ["../src/filter-items.ts"], "sourcesContent": ["import semver from \"semver\";\n\nimport pluginsCompatData from \"@babel/compat-data/plugins\";\n\nimport type { Targets } from \"./types.ts\";\nimport {\n  getLowestImplementedVersion,\n  isUnreleasedVersion,\n  semverify,\n} from \"./utils.ts\";\n\nexport function targetsSupported(target: Targets, support: Targets) {\n  const targetEnvironments = Object.keys(target) as Array<keyof Targets>;\n\n  if (targetEnvironments.length === 0) {\n    return false;\n  }\n\n  const unsupportedEnvironments = targetEnvironments.filter(environment => {\n    const lowestImplementedVersion = getLowestImplementedVersion(\n      support,\n      environment,\n    );\n\n    // Feature is not implemented in that environment\n    if (!lowestImplementedVersion) {\n      return true;\n    }\n\n    const lowestTargetedVersion = target[environment];\n\n    // If targets has unreleased value as a lowest version, then don't require a plugin.\n    if (isUnreleasedVersion(lowestTargetedVersion, environment)) {\n      return false;\n    }\n\n    // Include plugin if it is supported in the unreleased environment, which wasn't specified in targets\n    if (isUnreleasedVersion(lowestImplementedVersion, environment)) {\n      return true;\n    }\n\n    if (!semver.valid(lowestTargetedVersion.toString())) {\n      throw new Error(\n        `Invalid version passed for target \"${environment}\": \"${lowestTargetedVersion}\". ` +\n          \"Versions must be in semver format (major.minor.patch)\",\n      );\n    }\n\n    return semver.gt(\n      semverify(lowestImplementedVersion),\n      lowestTargetedVersion.toString(),\n    );\n  });\n\n  return unsupportedEnvironments.length === 0;\n}\n\nexport function isRequired(\n  name: string,\n  targets: Targets,\n  {\n    compatData = pluginsCompatData,\n    includes,\n    excludes,\n  }: {\n    compatData?: { [feature: string]: Targets };\n    includes?: Set<string>;\n    excludes?: Set<string>;\n  } = {},\n) {\n  if (excludes?.has(name)) return false;\n  if (includes?.has(name)) return true;\n  return !targetsSupported(targets, compatData[name]);\n}\n\nexport default function filterItems(\n  list: { [feature: string]: Targets },\n  includes: Set<string>,\n  excludes: Set<string>,\n  targets: Targets,\n  defaultIncludes: Array<string> | null,\n  defaultExcludes?: Array<string> | null,\n  pluginSyntaxMap?: Map<string, string | null>,\n) {\n  const result = new Set<string>();\n  const options = { compatData: list, includes, excludes };\n\n  for (const item in list) {\n    if (isRequired(item, targets, options)) {\n      result.add(item);\n    } else if (pluginSyntaxMap) {\n      const shippedProposalsSyntax = pluginSyntaxMap.get(item);\n\n      if (shippedProposalsSyntax) {\n        result.add(shippedProposalsSyntax);\n      }\n    }\n  }\n\n  defaultIncludes?.forEach(item => !excludes.has(item) && result.add(item));\n  defaultExcludes?.forEach(item => !includes.has(item) && result.delete(item));\n\n  return result;\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAGA,IAAAE,MAAA,GAAAF,OAAA;AAMO,SAASG,gBAAgBA,CAACC,MAAe,EAAEC,OAAgB,EAAE;EAClE,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAyB;EAEtE,IAAIE,kBAAkB,CAACG,MAAM,KAAK,CAAC,EAAE;IACnC,OAAO,KAAK;EACd;EAEA,MAAMC,uBAAuB,GAAGJ,kBAAkB,CAACK,MAAM,CAACC,WAAW,IAAI;IACvE,MAAMC,wBAAwB,GAAG,IAAAC,kCAA2B,EAC1DT,OAAO,EACPO,WACF,CAAC;IAGD,IAAI,CAACC,wBAAwB,EAAE;MAC7B,OAAO,IAAI;IACb;IAEA,MAAME,qBAAqB,GAAGX,MAAM,CAACQ,WAAW,CAAC;IAGjD,IAAI,IAAAI,0BAAmB,EAACD,qBAAqB,EAAEH,WAAW,CAAC,EAAE;MAC3D,OAAO,KAAK;IACd;IAGA,IAAI,IAAAI,0BAAmB,EAACH,wBAAwB,EAAED,WAAW,CAAC,EAAE;MAC9D,OAAO,IAAI;IACb;IAEA,IAAI,CAACK,OAAM,CAACC,KAAK,CAACH,qBAAqB,CAACI,QAAQ,CAAC,CAAC,CAAC,EAAE;MACnD,MAAM,IAAIC,KAAK,CACb,sCAAsCR,WAAW,OAAOG,qBAAqB,KAAK,GAChF,uDACJ,CAAC;IACH;IAEA,OAAOE,OAAM,CAACI,EAAE,CACd,IAAAC,gBAAS,EAACT,wBAAwB,CAAC,EACnCE,qBAAqB,CAACI,QAAQ,CAAC,CACjC,CAAC;EACH,CAAC,CAAC;EAEF,OAAOT,uBAAuB,CAACD,MAAM,KAAK,CAAC;AAC7C;AAEO,SAASc,UAAUA,CACxBC,IAAY,EACZC,OAAgB,EAChB;EACEC,UAAU,GAAGC,QAAiB;EAC9BC,QAAQ;EACRC;AAKF,CAAC,GAAG,CAAC,CAAC,EACN;EACA,IAAIA,QAAQ,YAARA,QAAQ,CAAEC,GAAG,CAACN,IAAI,CAAC,EAAE,OAAO,KAAK;EACrC,IAAII,QAAQ,YAARA,QAAQ,CAAEE,GAAG,CAACN,IAAI,CAAC,EAAE,OAAO,IAAI;EACpC,OAAO,CAACrB,gBAAgB,CAACsB,OAAO,EAAEC,UAAU,CAACF,IAAI,CAAC,CAAC;AACrD;AAEe,SAASO,WAAWA,CACjCC,IAAoC,EACpCJ,QAAqB,EACrBC,QAAqB,EACrBJ,OAAgB,EAChBQ,eAAqC,EACrCC,eAAsC,EACtCC,eAA4C,EAC5C;EACA,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAS,CAAC;EAChC,MAAMC,OAAO,GAAG;IAAEZ,UAAU,EAAEM,IAAI;IAAEJ,QAAQ;IAAEC;EAAS,CAAC;EAExD,KAAK,MAAMU,IAAI,IAAIP,IAAI,EAAE;IACvB,IAAIT,UAAU,CAACgB,IAAI,EAAEd,OAAO,EAAEa,OAAO,CAAC,EAAE;MACtCF,MAAM,CAACI,GAAG,CAACD,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIJ,eAAe,EAAE;MAC1B,MAAMM,sBAAsB,GAAGN,eAAe,CAACO,GAAG,CAACH,IAAI,CAAC;MAExD,IAAIE,sBAAsB,EAAE;QAC1BL,MAAM,CAACI,GAAG,CAACC,sBAAsB,CAAC;MACpC;IACF;EACF;EAEAR,eAAe,YAAfA,eAAe,CAAEU,OAAO,CAACJ,IAAI,IAAI,CAACV,QAAQ,CAACC,GAAG,CAACS,IAAI,CAAC,IAAIH,MAAM,CAACI,GAAG,CAACD,IAAI,CAAC,CAAC;EACzEL,eAAe,YAAfA,eAAe,CAAES,OAAO,CAACJ,IAAI,IAAI,CAACX,QAAQ,CAACE,GAAG,CAACS,IAAI,CAAC,IAAIH,MAAM,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAC;EAE5E,OAAOH,MAAM;AACf", "ignoreList": []}