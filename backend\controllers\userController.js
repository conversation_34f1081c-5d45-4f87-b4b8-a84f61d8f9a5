const { User } = require('../models/User');
const { Relationship } = require('../models/Relationship');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Get all users
exports.getAllUsers = async (req, res, next) => {
  try {
    const users = await User.findAll({
      attributes: ['id', 'username', 'email', 'user_type', 'created_at']
    });

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (error) {
    next(error);
  }
};

// Get user by ID
exports.getUserById = async (req, res, next) => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: ['id', 'username', 'email', 'user_type', 'created_at']
    });

    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Check if user is requesting their own data or has a relationship
    if (req.user.id !== user.id) {
      // If parent requesting child data
      if (req.user.user_type === 'parent' && user.user_type === 'child') {
        const relationship = await Relationship.findOne({
          where: {
            parent_id: req.user.id,
            child_id: user.id
          }
        });

        if (!relationship) {
          return next(new AppError('Not authorized to access this user', 403));
        }
      } 
      // If child requesting parent data
      else if (req.user.user_type === 'child' && user.user_type === 'parent') {
        const relationship = await Relationship.findOne({
          where: {
            parent_id: user.id,
            child_id: req.user.id
          }
        });

        if (!relationship) {
          return next(new AppError('Not authorized to access this user', 403));
        }
      }
      // Any other case is unauthorized
      else {
        return next(new AppError('Not authorized to access this user', 403));
      }
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// Update user
exports.updateUser = async (req, res, next) => {
  try {
    // Check if user exists
    const user = await User.findByPk(req.params.id);

    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Check if user is updating their own data
    if (req.user.id !== user.id) {
      return next(new AppError('Not authorized to update this user', 403));
    }

    // Update user
    const { username, email } = req.body;

    if (username) user.username = username;
    if (email) user.email = email;

    await user.save();

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        userType: user.user_type
      }
    });
  } catch (error) {
    next(error);
  }
};

// Delete user
exports.deleteUser = async (req, res, next) => {
  try {
    // Check if user exists
    const user = await User.findByPk(req.params.id);

    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Check if user is deleting their own account
    if (req.user.id !== user.id) {
      return next(new AppError('Not authorized to delete this user', 403));
    }

    // Delete user
    await user.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// Get children for parent
exports.getParentChildren = async (req, res, next) => {
  try {
    // Get relationships
    const relationships = await Relationship.findAll({
      where: { parent_id: req.user.id },
      include: [
        {
          model: User,
          as: 'child',
          attributes: ['id', 'username', 'email', 'created_at']
        }
      ]
    });

    // Extract children from relationships
    const children = relationships.map(relationship => ({
      ...relationship.child.toJSON(),
      isPrimaryParent: relationship.is_primary_parent
    }));

    res.status(200).json({
      success: true,
      count: children.length,
      data: children
    });
  } catch (error) {
    next(error);
  }
};

// Get parents for child
exports.getChildParents = async (req, res, next) => {
  try {
    // Get relationships
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id },
      include: [
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'username', 'email', 'created_at']
        }
      ]
    });

    // Extract parents from relationships
    const parents = relationships.map(relationship => ({
      ...relationship.parent.toJSON(),
      isPrimaryParent: relationship.is_primary_parent
    }));

    res.status(200).json({
      success: true,
      count: parents.length,
      data: parents
    });
  } catch (error) {
    next(error);
  }
};
