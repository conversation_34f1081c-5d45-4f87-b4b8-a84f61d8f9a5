const { validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Middleware to validate request data
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg
    }));
    
    return next(new AppError('Validation error', 400, errorMessages));
  }
  next();
};

module.exports = { validate };
