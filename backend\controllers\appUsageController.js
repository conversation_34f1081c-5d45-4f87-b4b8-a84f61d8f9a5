const { sequelize } = require('../config/database');
const logger = require('../utils/logger');
const AppError = require('../utils/appError');
const { protect } = require('../middleware/auth');

/**
 * Upload app usage data
 */
exports.uploadAppUsage = async (req, res, next) => {
  try {
    // Get user ID from auth token
    const userId = req.user.id;

    // Get app usage data from request body
    const appUsageDataList = req.body;

    // Log the request for debugging
    logger.debug(`Received app usage data for user ${userId}: ${JSON.stringify(appUsageDataList).substring(0, 200)}...`);

    if (!Array.isArray(appUsageDataList) || appUsageDataList.length === 0) {
      return next(new AppError('Invalid app usage data', 400));
    }

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      // Process each app usage entry
      for (const appUsage of appUsageDataList) {
        const { packageName, appName, usageTimeMs } = appUsage;

        // Validate required fields
        if (!packageName || !appName || typeof usageTimeMs !== 'number') {
          continue; // Skip invalid entries
        }

        // Check if app exists in the database
        const [appResults] = await sequelize.query(
          `SELECT id FROM apps WHERE package_name = $1`,
          {
            bind: [packageName],
            transaction
          }
        );

        let appId;

        if (appResults.length === 0) {
          // App doesn't exist, create it
          const [insertResult] = await sequelize.query(
            `INSERT INTO apps (package_name, app_name, icon_url, created_at)
             VALUES ($1, $2, $3, NOW())
             RETURNING id`,
            {
              bind: [packageName, appName, null], // No icon URL from Android app
              transaction
            }
          );

          appId = insertResult[0].id;
        } else {
          appId = appResults[0].id;
        }

        // Insert or update app usage data
        await sequelize.query(
          `INSERT INTO child_apps (child_id, app_id, usage_time, updated_at)
           VALUES ($1, $2, $3, NOW())
           ON CONFLICT (child_id, app_id)
           DO UPDATE SET
             usage_time = $3,
             updated_at = NOW()`,
          {
            bind: [userId, appId, usageTimeMs],
            transaction
          }
        );
      }

      // Commit transaction
      await transaction.commit();

      // Log success
      logger.info(`App usage data uploaded for user ${userId}`);

      // Return success response
      return res.status(200).json({
        success: true,
        data: null
      });
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    // Log detailed error information
    logger.error(`Error uploading app usage data: ${error.message}`);
    if (error.name === 'SequelizeValidationError' || error.name === 'SequelizeUniqueConstraintError') {
      // Handle validation errors
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      logger.error(`Validation errors: ${JSON.stringify(validationErrors)}`);
      return next(new AppError('Validation error in app usage data', 400));
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      // Handle foreign key constraint errors
      logger.error(`Foreign key constraint error: ${error.fields}`);
      return next(new AppError('Referenced entity does not exist', 400));
    }
    return next(new AppError('Failed to upload app usage data', 500));
  }
};

/**
 * Get app usage data for a child
 */
exports.getChildAppUsage = async (req, res, next) => {
  try {
    const { childId } = req.params;
    const parentId = req.user.id;

    // Verify parent-child relationship
    const [relationshipResults] = await sequelize.query(
      `SELECT * FROM relationships
       WHERE parent_id = $1 AND child_id = $2`,
      { bind: [parentId, childId] }
    );

    if (relationshipResults.length === 0) {
      return next(new AppError('Child not found or not associated with this parent', 404));
    }

    // Get app usage data
    const [appUsageResults] = await sequelize.query(
      `SELECT a.id, a.package_name, a.app_name, a.icon_url,
              ca.usage_time,
              ca.start_time, ca.end_time, ca.blocked as is_blocked
       FROM child_apps ca
       JOIN apps a ON ca.app_id = a.id
       WHERE ca.child_id = $1
       ORDER BY ca.usage_time DESC`,
      { bind: [childId] }
    );

    // Return app usage data
    return res.status(200).json({
      success: true,
      data: appUsageResults
    });
  } catch (error) {
    logger.error(`Error getting child app usage: ${error.message}`);
    return next(new AppError('Failed to get child app usage data', 500));
  }
};

/**
 * Update app control settings
 */
exports.updateAppControl = async (req, res, next) => {
  try {
    const { childId, appId } = req.params;
    const parentId = req.user.id;
    const { isBlocked, startTime, endTime } = req.body;

    // Verify parent-child relationship
    const [relationshipResults] = await sequelize.query(
      `SELECT * FROM relationships
       WHERE parent_id = $1 AND child_id = $2`,
      { bind: [parentId, childId] }
    );

    if (relationshipResults.length === 0) {
      return next(new AppError('Child not found or not associated with this parent', 404));
    }

    // Verify app exists for this child
    const [appResults] = await sequelize.query(
      `SELECT * FROM child_apps
       WHERE child_id = $1 AND app_id = $2`,
      { bind: [childId, appId] }
    );

    if (appResults.length === 0) {
      return next(new AppError('App not found for this child', 404));
    }

    // Update app control settings
    await sequelize.query(
      `UPDATE child_apps
       SET blocked = $1,
           start_time = $2,
           end_time = $3,
           updated_at = NOW()
       WHERE child_id = $4 AND app_id = $5`,
      {
        bind: [
          isBlocked !== undefined ? isBlocked : appResults[0].is_blocked,
          startTime || appResults[0].start_time,
          endTime || appResults[0].end_time,
          childId,
          appId
        ]
      }
    );

    // Return success response
    return res.status(200).json({
      success: true,
      data: null
    });
  } catch (error) {
    logger.error(`Error updating app control: ${error.message}`);
    return next(new AppError('Failed to update app control settings', 500));
  }
};
