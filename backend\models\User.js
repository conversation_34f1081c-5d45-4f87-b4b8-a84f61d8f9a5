const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  user_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['parent', 'child']]
    }
  },
  guardian_type: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: null,
    validate: {
      isIn: [[null, 'main_guardian', 'secondary_guardian']]
    }
  },
  registration_code: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  registration_code_expires_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  registration_code_used: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  access_level: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeSave: async (user) => {
      // Only hash password if it's new or modified
      if (user.changed('password_hash')) {
        const salt = await bcrypt.genSalt(10);
        user.password_hash = await bcrypt.hash(user.password_hash, salt);
      }
    }
  }
});

// Instance method to check if password matches
User.prototype.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password_hash);
};

// Generate a registration code for a child
User.prototype.generateRegistrationCode = async function() {
  // Generate a random 9-digit code
  const code = Math.floor(100000000 + Math.random() * 900000000).toString();

  // Set expiration to 24 hours from now
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24);

  // Update the user with the new code
  this.registration_code = code;
  this.registration_code_expires_at = expiresAt;
  this.registration_code_used = false;

  await this.save();

  return this;
};

// Check if a registration code is valid
User.validateRegistrationCode = async function(code) {
  const user = await this.findOne({ where: { registration_code: code } });

  if (!user) {
    throw new Error('Invalid registration code');
  }

  if (user.user_type !== 'child') {
    throw new Error('Registration code is not associated with a child account');
  }

  if (user.registration_code_used) {
    throw new Error('Registration code has already been used');
  }

  const now = new Date();
  const expiresAt = new Date(user.registration_code_expires_at);

  if (now > expiresAt) {
    throw new Error('Registration code has expired');
  }

  return user;
};

// Use a registration code
User.useRegistrationCode = async function(code, deviceId) {
  const user = await this.validateRegistrationCode(code);

  // Mark the code as used
  user.registration_code_used = true;
  await user.save();

  // Return the user
  return user;
};

module.exports = { User };
