const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');
const { App } = require('./App');

const ChildApp = sequelize.define('ChildApp', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  child_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  app_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: App,
      key: 'id'
    }
  },
  usage_time: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Usage time in seconds'
  },
  time_limit: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Time limit in seconds'
  },
  blocked: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  start_time: {
    type: DataTypes.TIME,
    allowNull: true
  },
  end_time: {
    type: DataTypes.TIME,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'child_apps',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['child_id', 'app_id']
    }
  ]
});

// Define associations
ChildApp.belongsTo(User, { foreignKey: 'child_id', as: 'child' });
ChildApp.belongsTo(App, { foreignKey: 'app_id', as: 'app' });

module.exports = { ChildApp };
