const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { ChildApp } = require('./ChildApp');
const { User } = require('./User');

const AppRestrictionHistory = sequelize.define('AppRestrictionHistory', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  child_app_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: ChildApp,
      key: 'id'
    }
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  action: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  previous_value: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  new_value: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'app_restrictions_history',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false
});

// Define associations
AppRestrictionHistory.belongsTo(ChildApp, { foreignKey: 'child_app_id', as: 'childApp' });
AppRestrictionHistory.belongsTo(User, { foreignKey: 'parent_id', as: 'parent' });

module.exports = { AppRestrictionHistory };
