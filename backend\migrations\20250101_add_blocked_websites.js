const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('blocked_websites', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      child_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'CASCADE'
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      blocked: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      }
    });

    // Add unique constraint
    await queryInterface.addIndex('blocked_websites', ['child_id', 'domain'], {
      unique: true,
      name: 'unique_child_domain'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('blocked_websites');
  }
};