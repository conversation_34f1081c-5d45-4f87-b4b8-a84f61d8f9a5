const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const { User } = require('../models/User');
const { AuthToken } = require('../models/AuthToken');
const { ReferralCode } = require('../models/ReferralCode');
const { GuardianRelationship } = require('../models/GuardianRelationship');
const { Relationship } = require('../models/Relationship');
const { AppError } = require('../middleware/errorHandler');
const { secret, expiresIn, refreshSecret, refreshExpiresIn } = require('../config/jwt');
const logger = require('../utils/logger');

// Generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, secret, {
    expiresIn
  });
};

// Generate refresh token
const generateRefreshToken = (id) => {
  return jwt.sign({ id }, refreshSecret, {
    expiresIn: refreshExpiresIn
  });
};

// Register a new user
exports.register = async (req, res, next) => {
  try {
    const { username, email, password, userType, referralCode } = req.body;

    // For requests coming from Flutter app, force userType to be 'parent'
    // You can detect Flutter app by checking user agent or by adding a custom header
    const isFlutterApp = req.headers['x-app-type'] === 'flutter-parent' ||
                         req.headers['user-agent']?.includes('Flutter');

    const finalUserType = isFlutterApp ? 'parent' : userType;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return next(new AppError('User already exists', 400));
    }

    // Check referral code if provided
    let referralCodeRecord = null;
    if (referralCode && finalUserType === 'parent') {
      referralCodeRecord = await ReferralCode.findOne({
        where: { code: referralCode },
        include: [{ model: User, as: 'creator', attributes: ['id', 'username', 'email'] }]
      });

      if (!referralCodeRecord) {
        return next(new AppError('Invalid referral code', 400));
      }

      // Check if code is expired
      if (new Date() > new Date(referralCodeRecord.expires_at)) {
        return next(new AppError('Referral code has expired', 400));
      }

      // Check if code is already used
      if (referralCodeRecord.used_by) {
        return next(new AppError('Referral code has already been used', 400));
      }
    }

    // Create user with appropriate guardian type
    const guardianType = referralCodeRecord ? 'secondary_guardian' : (finalUserType === 'parent' ? 'main_guardian' : null);

    // Set access_level=true for main guardians, false for others
    const accessLevel = (guardianType === 'main_guardian');

    const user = await User.create({
      username,
      email,
      password_hash: password,
      user_type: finalUserType,
      guardian_type: guardianType,
      access_level: accessLevel
    });

    // Generate tokens
    const token = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Calculate token expiry
    const tokenExpiry = new Date();
    tokenExpiry.setDate(tokenExpiry.getDate() + 7); // 7 days from now

    // Save token to database
    await AuthToken.create({
      user_id: user.id,
      token,
      device_info: req.headers['user-agent'],
      expires_at: tokenExpiry
    });

    // If referral code was used, update it and create guardian relationship
    if (referralCodeRecord) {
      // Update referral code with the user who used it
      referralCodeRecord.used_by = user.id;
      await referralCodeRecord.save();

      // Create guardian relationship
      await GuardianRelationship.create({
        main_guardian_id: referralCodeRecord.creator_id,
        secondary_guardian_id: user.id,
        access_level: referralCodeRecord.access_level
      });

      // Get all children of the main guardian to share with the secondary guardian
      const mainGuardianChildren = await Relationship.findAll({
        where: { parent_id: referralCodeRecord.creator_id }
      });

      logger.info(`Sharing ${mainGuardianChildren.length} children with secondary guardian ${user.id}`);
    }

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          userType: user.user_type,
          guardianType: user.guardian_type,
          accessLevel: user.access_level
        },
        token,
        refreshToken,
        referralUsed: referralCodeRecord ? true : false
      }
    });
  } catch (error) {
    next(error);
  }
};

// Login user
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return next(new AppError('Invalid credentials', 401));
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      return next(new AppError('Invalid credentials', 401));
    }

    // Generate tokens
    const token = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Calculate token expiry
    const tokenExpiry = new Date();
    tokenExpiry.setDate(tokenExpiry.getDate() + 7); // 7 days from now

    // Save token to database
    await AuthToken.create({
      user_id: user.id,
      token,
      device_info: req.headers['user-agent'],
      expires_at: tokenExpiry
    });

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          userType: user.user_type,
          guardianType: user.guardian_type,
          accessLevel: user.access_level
        },
        token,
        refreshToken
      }
    });
  } catch (error) {
    next(error);
  }
};

// Refresh token
exports.refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    // Verify refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, refreshSecret);
    } catch (error) {
      return next(new AppError('Invalid refresh token', 401));
    }

    // Get user
    const user = await User.findByPk(decoded.id);
    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Generate new tokens
    const token = generateToken(user.id);
    const newRefreshToken = generateRefreshToken(user.id);

    // Calculate token expiry
    const tokenExpiry = new Date();
    tokenExpiry.setDate(tokenExpiry.getDate() + 7); // 7 days from now

    // Save token to database
    await AuthToken.create({
      user_id: user.id,
      token,
      device_info: req.headers['user-agent'],
      expires_at: tokenExpiry
    });

    res.status(200).json({
      success: true,
      data: {
        token,
        refreshToken: newRefreshToken
      }
    });
  } catch (error) {
    next(error);
  }
};

// Logout user
exports.logout = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    let token;

    if (authHeader && authHeader.startsWith('Bearer')) {
      token = authHeader.split(' ')[1];
    }

    if (!token) {
      return next(new AppError('No token provided', 400));
    }

    // Delete token from database
    await AuthToken.destroy({
      where: {
        token,
        user_id: req.user.id
      }
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// Get current user
exports.getMe = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          username: req.user.username,
          email: req.user.email,
          userType: req.user.user_type,
          guardianType: req.user.guardian_type,
          accessLevel: req.user.access_level
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Use registration code to register a child device
exports.useRegistrationCode = async (req, res, next) => {
  try {
    const { code } = req.params;
    const { deviceId } = req.body;

    if (!code || !deviceId) {
      return next(new AppError('Registration code and device ID are required', 400));
    }

    // Find user with this registration code
    const user = await User.findOne({
      where: {
        registration_code: code,
        user_type: 'child',
        registration_code_used: false
      }
    });

    if (!user) {
      return next(new AppError('Invalid or expired registration code', 400));
    }

    // Check if code is expired
    if (new Date() > new Date(user.registration_code_expires_at)) {
      return next(new AppError('Registration code has expired', 400));
    }

    // Mark code as used
    user.registration_code_used = true;
    user.device_id = deviceId;
    await user.save();

    // Generate tokens
    const token = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Calculate token expiry
    const tokenExpiry = new Date();
    tokenExpiry.setDate(tokenExpiry.getDate() + 30); // 30 days for child devices

    // Save token to database
    await AuthToken.create({
      user_id: user.id,
      token,
      device_info: req.headers['user-agent'] || 'Android Child App',
      device_id: deviceId,
      expires_at: tokenExpiry
    });

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          userType: user.user_type
        },
        token,
        refreshToken
      }
    });
  } catch (error) {
    logger.error(`Error using registration code: ${error.message}`);
    next(error);
  }
};
