const jwt = require('jsonwebtoken');
const { User } = require('../models/User');
const { Relationship } = require('../models/Relationship');
const { AuthToken } = require('../models/AuthToken');
const { secret, expiresIn } = require('../config/jwt');
const { AppError } = require('../middleware/errorHandler');

// Generate JWT token (copied from authController.js)
const generateToken = (id) => {
  return jwt.sign({ id }, secret, {
    expiresIn
  });
};

/**
 * @desc    Generate a registration code for a child
 * @route   POST /api/users/:childId/registration-code
 * @access  Private (Parent only)
 */
exports.generateCode = async (req, res, next) => {
  try {
    const { childId } = req.params;

    // Check if the child exists and is a child user
    const child = await User.findByPk(childId);

    if (!child) {
      return res.status(404).json({
      success: false,
      error: {
        message: 'Child not found'
      }
    });
  }

    if (child.user_type !== 'child') {
      return res.status(400).json({
      success: false,
      error: {
        message: 'User is not a child'
      }
    });
  }

    // Check if the requesting user is a parent of this child
    const relationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childId
      }
    });

    if (!relationship) {
      return res.status(403).json({
      success: false,
      error: {
        message: 'You are not authorized to generate a registration code for this child'
      }
      });
    }

    // Generate a registration code
    await child.generateRegistrationCode();

    // Return the updated child with the registration code
    return res.status(201).json({
      success: true,
      data: {
        id: child.id,
        username: child.username,
        registrationCode: child.registration_code,
        registrationCodeExpiresAt: child.registration_code_expires_at,
        registrationCodeUsed: child.registration_code_used
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Validate a registration code
 * @route   GET /api/users/validate-code/:code
 * @access  Public
 */
exports.validateCode = async (req, res, next) => {
  try {
    const { code } = req.params;

    try {
      // Validate the code
      const child = await User.validateRegistrationCode(code);

      // Return basic child information
      return res.status(200).json({
        success: true,
        data: {
          id: child.id,
          username: child.username,
          registrationCode: child.registration_code,
          registrationCodeExpiresAt: child.registration_code_expires_at
        }
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Invalid registration code'
        }
      });
  }
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Use a registration code to register a device
 * @route   POST /api/users/use-code/:code
 * @access  Public
 */
exports.useCode = async (req, res, next) => {
  try {
    const { code } = req.params;
    const { deviceId } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Device ID is required'
        }
      });
    }

    try {
      // Use the code
      const child = await User.useRegistrationCode(code, deviceId);

      // Generate authentication token for the child
      const token = generateToken(child.id);

      // Calculate token expiry
      const tokenExpiry = new Date();
      tokenExpiry.setDate(tokenExpiry.getDate() + 7); // 7 days from now

      // Save token to database
      await AuthToken.create({
        user_id: child.id,
        token,
        device_info: req.headers['user-agent'] || 'Child Device',
        expires_at: tokenExpiry
      });

      // Return authentication information
      return res.status(200).json({
        success: true,
        data: {
          user: {
            id: child.id,
            username: child.username,
            email: child.email,
            userType: child.user_type
          },
          token
        }
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to use registration code'
        }
      });
  }
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get registration code for a child
 * @route   GET /api/users/:childId/registration-code
 * @access  Private (Parent only)
 */
exports.getChildCode = async (req, res, next) => {
  try {
    const { childId } = req.params;

    // Check if the child exists
    const child = await User.findByPk(childId);

    if (!child) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Child not found'
        }
      });
    }

    // Check if the requesting user is a parent of this child
    const relationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childId
      }
    });

    if (!relationship) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'You are not authorized to view registration codes for this child'
        }
      });
    }

    // For our simplified approach, we only have one code per child
    // So we'll just return the child's current code if it exists
    if (child.registration_code) {
      return res.status(200).json({
        success: true,
        data: [{
          id: child.id,
          childId: child.id,
          code: child.registration_code,
          createdAt: child.updated_at,
          expiresAt: child.registration_code_expires_at,
          isUsed: child.registration_code_used
        }]
      });
    } else {
      return res.status(200).json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Invalidate a registration code
 * @route   DELETE /api/users/:childId/registration-code
 * @access  Private (Parent only)
 */
exports.invalidateCode = async (req, res, next) => {
  try {
    const { childId } = req.params;

    // Check if the child exists
    const child = await User.findByPk(childId);

    if (!child) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'Child not found'
        }
      });
    }

    // Check if the requesting user is a parent of this child
    const relationship = await Relationship.findOne({
      where: {
        parent_id: req.user.id,
        child_id: childId
      }
    });

    if (!relationship) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'You are not authorized to invalidate registration codes for this child'
        }
      });
    }

    // Invalidate the code by setting it to null
    child.registration_code = null;
    child.registration_code_expires_at = null;
    child.registration_code_used = false;
    await child.save();

    return res.status(200).json({
      success: true,
      message: 'Registration code invalidated successfully'
  });
  } catch (error) {
    next(error);
  }
};