{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:37:24.209Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:54:45.176Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:55:17.518Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:56:07.816Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:57:43.329Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:57:56.463Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:58:14.583Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:84:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T18:03:34.055Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:84:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T18:08:59.196Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/auth/me","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-04-12T19:07:47.327Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:07:47.335Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:08:06.065Z"}
{"fields":null,"index":"parent_child_relationships_parent_id_fkey","level":"error","message":"Unable to connect to the database: insert or update on table \"parent_child_relationships\" violates foreign key constraint \"parent_child_relationships_parent_id_fkey\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"parameters":{},"parent":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"service":"digital-wellbeing-api","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","table":"parent_child_relationships","timestamp":"2025-04-12T19:26:35.458Z"}
{"fields":null,"index":"parent_child_relationships_parent_id_fkey","level":"error","message":"Unable to connect to the database: insert or update on table \"parent_child_relationships\" violates foreign key constraint \"parent_child_relationships_parent_id_fkey\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"parameters":{},"parent":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"service":"digital-wellbeing-api","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","table":"parent_child_relationships","timestamp":"2025-04-12T19:29:07.469Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/auth/me","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-04-12T19:36:55.601Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:36:55.615Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:50:16.640Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:56:19.416Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:17.029Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:22.391Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:23.571Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T20:12:40.458Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:65:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:50:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T06:47:37.015Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:83:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:57:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T07:09:15.799Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:90:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:57:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T07:19:54.579Z"}
{"level":"error","message":"SequelizeDatabaseError: invalid input syntax for type integer: \"block\"","method":"PUT","path":"/api/child-apps/block","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.select (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async ChildApp.findAll (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async ChildApp.findOne (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1240:12)\n    at async ChildApp.findByPk (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1221:12)\n    at async exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:140:22)","timestamp":"2025-04-13T09:12:28.024Z"}
{"level":"error","message":"SequelizeDatabaseError: invalid input syntax for type time: \"540\"","method":"PUT","path":"/api/child-apps/14","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:179:5)","timestamp":"2025-04-13T09:19:03.998Z"}
{"level":"error","message":"Failed to create migrations table: password authentication failed for user \"postgres\"","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:12:10.889Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:21:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-13T12:39:10.847Z"}
{"level":"error","message":"SequelizeDatabaseError: relation \"parent_child_relationships\" does not exist","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.select (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Relationship.findAll (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async exports.getParentRelationships (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\relationshipController.js:10:27)","timestamp":"2025-04-13T12:58:38.443Z"}
{"level":"error","message":"Unable to connect to the database: relation \"relationships_parent_id_child_id\" already exists","name":"SequelizeDatabaseError","original":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"parameters":{},"parent":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"service":"digital-wellbeing-api","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.addIndex (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","timestamp":"2025-04-13T12:59:40.189Z"}
{"level":"error","message":"Unable to connect to the database: relation \"relationships_parent_id_child_id\" already exists","name":"SequelizeDatabaseError","original":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"parameters":{},"parent":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"service":"digital-wellbeing-api","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.addIndex (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","timestamp":"2025-04-13T13:00:16.952Z"}
{"level":"error","message":"AppError: Referral code has already been used","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Referral code has already been used\n    at exports.register (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:63:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:06:38.810Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/2","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:52:34.152Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:53:48.334Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:33.968Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:37.459Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:39.416Z"}
{"level":"error","message":"AppError: Not authorized to update this child app","method":"PUT","path":"/api/child-apps/27","service":"digital-wellbeing-api","stack":"AppError: Not authorized to update this child app\n    at exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:298:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T14:17:13.698Z"}
{"level":"error","message":"Migration process failed: Cannot read properties of undefined (reading 'addColumn')","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:57:23.244Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:143:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T06:35:23.084Z"}
{"level":"error","message":"Error uploading app usage data: column \"usage_time_ms\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:49:29.312Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:49:29.315Z"}
{"level":"error","message":"Error uploading app usage data: column \"usage_time_ms\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:49:29.321Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:49:29.323Z"}
{"level":"error","message":"Error uploading app usage data: column \"last_used_timestamp\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:56:25.714Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:56:25.721Z"}
{"level":"error","message":"Error uploading app usage data: column \"last_used_timestamp\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:56:25.742Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:56:25.744Z"}
{"level":"error","message":"Error uploading app usage data: Validation error","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:30.890Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:95:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T09:03:30.892Z"}
{"level":"error","message":"Error: incorrect header check","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: incorrect header check\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at Zlib.zlibOnError [as onerror] (node:zlib:185:17)","timestamp":"2025-04-19T10:09:59.603Z"}
{"level":"error","message":"Failed to create migrations table: Access denied for user 'root'@'localhost' (using password: YES)","service":"digital-wellbeing-api","timestamp":"2025-08-01T09:34:18.232Z"}
{"level":"error","message":"Migration failed: Duplicate column name 'guardian_type'","service":"digital-wellbeing-api","timestamp":"2025-08-01T09:36:06.369Z"}
