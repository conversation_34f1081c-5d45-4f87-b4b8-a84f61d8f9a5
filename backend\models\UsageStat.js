const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');
const { App } = require('./App');

const UsageStat = sequelize.define('UsageStat', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  child_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  app_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: App,
      key: 'id'
    }
  },
  usage_time: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Usage time in seconds'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'usage_stats',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      unique: true,
      fields: ['child_id', 'app_id', 'date']
    }
  ]
});

// Define associations
UsageStat.belongsTo(User, { foreignKey: 'child_id', as: 'child' });
UsageStat.belongsTo(App, { foreignKey: 'app_id', as: 'app' });

module.exports = { UsageStat };
