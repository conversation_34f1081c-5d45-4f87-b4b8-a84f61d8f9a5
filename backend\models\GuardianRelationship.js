const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');

const GuardianRelationship = sequelize.define('GuardianRelationship', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  main_guardian_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  secondary_guardian_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  access_level: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'read_only',
    validate: {
      isIn: [['read_only', 'full_access']]
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'guardian_relationships',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  indexes: [
    {
      unique: true,
      fields: ['main_guardian_id', 'secondary_guardian_id']
    }
  ]
});

// Define associations
GuardianRelationship.belongsTo(User, { foreignKey: 'main_guardian_id', as: 'mainGuardian' });
GuardianRelationship.belongsTo(User, { foreignKey: 'secondary_guardian_id', as: 'secondaryGuardian' });

// Add reverse associations to User model
User.hasMany(GuardianRelationship, { foreignKey: 'main_guardian_id', as: 'secondaryGuardians' });
User.hasMany(GuardianRelationship, { foreignKey: 'secondary_guardian_id', as: 'mainGuardians' });

module.exports = { GuardianRelationship };
