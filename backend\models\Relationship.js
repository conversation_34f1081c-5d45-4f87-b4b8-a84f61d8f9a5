const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./User');

const Relationship = sequelize.define('Relationship', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  parent_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  child_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  is_primary_parent: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'relationships',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  // Skip index creation as it already exists in the database
  // indexes: [
  //   {
  //     unique: true,
  //     fields: ['parent_id', 'child_id']
  //   }
  // ]
});

// Define associations
Relationship.belongsTo(User, { foreignKey: 'parent_id', as: 'parent' });
Relationship.belongsTo(User, { foreignKey: 'child_id', as: 'child' });

// Add reverse associations to User model
User.hasMany(Relationship, { foreignKey: 'parent_id', as: 'children' });
User.hasMany(Relationship, { foreignKey: 'child_id', as: 'parents' });

module.exports = { Relationship };

