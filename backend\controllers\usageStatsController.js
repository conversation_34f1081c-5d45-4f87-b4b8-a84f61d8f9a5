const { Op } = require('sequelize');
const { UsageStat } = require('../models/UsageStat');
const { App } = require('../models/App');
const { User } = require('../models/User');
const { ChildApp } = require('../models/ChildApp');
const { Relationship } = require('../models/Relationship');
const { AppError } = require('../middleware/errorHandler');
const { getIo } = require('../socket');
const logger = require('../utils/logger');

// Get usage stats for a child
exports.getChildUsageStats = async (req, res, next) => {
  try {
    const { childId } = req.params;
    const { startDate, endDate } = req.query;

    // Check if user is authorized to access child's stats
    if (req.user.id !== parseInt(childId)) {
      // If parent, check relationship
      if (req.user.user_type === 'parent') {
        const relationship = await Relationship.findOne({
          where: {
            parent_id: req.user.id,
            child_id: childId
          }
        });

        if (!relationship) {
          return next(new AppError('Not authorized to access this child\'s stats', 403));
        }
      } else {
        return next(new AppError('Not authorized to access this child\'s stats', 403));
      }
    }

    // Build query
    const query = {
      where: { child_id: childId },
      include: [
        {
          model: App,
          as: 'app'
        }
      ],
      order: [['date', 'DESC']]
    };

    // Add date filters if provided
    if (startDate || endDate) {
      query.where.date = {};
      if (startDate) {
        query.where.date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        query.where.date[Op.lte] = new Date(endDate);
      }
    }

    // Get usage stats
    const usageStats = await UsageStat.findAll(query);

    // Format response
    const formattedStats = usageStats.map(stat => ({
      id: stat.id,
      appId: stat.app_id,
      packageName: stat.app.package_name,
      appName: stat.app.app_name,
      usageTime: stat.usage_time,
      date: stat.date
    }));

    res.status(200).json({
      success: true,
      count: formattedStats.length,
      data: formattedStats
    });
  } catch (error) {
    next(error);
  }
};

// Get usage stats for an app
exports.getAppUsageStats = async (req, res, next) => {
  try {
    const { appId, childId } = req.params;
    const { startDate, endDate } = req.query;

    // Check if user is authorized to access child's stats
    if (req.user.id !== parseInt(childId)) {
      // If parent, check relationship
      if (req.user.user_type === 'parent') {
        const relationship = await Relationship.findOne({
          where: {
            parent_id: req.user.id,
            child_id: childId
          }
        });

        if (!relationship) {
          return next(new AppError('Not authorized to access this child\'s stats', 403));
        }
      } else {
        return next(new AppError('Not authorized to access this child\'s stats', 403));
      }
    }

    // Build query
    const query = {
      where: {
        child_id: childId,
        app_id: appId
      },
      include: [
        {
          model: App,
          as: 'app'
        }
      ],
      order: [['date', 'DESC']]
    };

    // Add date filters if provided
    if (startDate || endDate) {
      query.where.date = {};
      if (startDate) {
        query.where.date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        query.where.date[Op.lte] = new Date(endDate);
      }
    }

    // Get usage stats
    const usageStats = await UsageStat.findAll(query);

    // Format response
    const formattedStats = usageStats.map(stat => ({
      id: stat.id,
      appId: stat.app_id,
      packageName: stat.app.package_name,
      appName: stat.app.app_name,
      usageTime: stat.usage_time,
      date: stat.date
    }));

    res.status(200).json({
      success: true,
      count: formattedStats.length,
      data: formattedStats
    });
  } catch (error) {
    next(error);
  }
};

// Create or update usage stat (child only)
exports.createOrUpdateUsageStat = async (req, res, next) => {
  try {
    const { appId, usageTime, date } = req.body;

    // Check if user is a child
    if (req.user.user_type !== 'child') {
      return next(new AppError('Only children can update usage stats', 403));
    }

    // Check if child has relationship with app
    const childApp = await ChildApp.findOne({
      where: {
        child_id: req.user.id,
        app_id: appId
      },
      include: [
        {
          model: App,
          as: 'app'
        }
      ]
    });

    if (!childApp) {
      return next(new AppError('App not found for this child', 404));
    }

    // Create or update usage stat
    const [usageStat, created] = await UsageStat.findOrCreate({
      where: {
        child_id: req.user.id,
        app_id: appId,
        date: new Date(date)
      },
      defaults: {
        usage_time: usageTime
      }
    });

    // If not created, update the usage time
    if (!created) {
      usageStat.usage_time = usageTime;
      await usageStat.save();
    }

    // Update the child app's current usage time
    childApp.usage_time = usageTime;
    await childApp.save();

    // Get parents of this child
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id }
    });

    // Notify parents via WebSocket
    const io = getIo();
    for (const relationship of relationships) {
      io.to(`user:${relationship.parent_id}`).emit('usage:updated', {
        childId: req.user.id,
        appId,
        packageName: childApp.app.package_name,
        usageTime,
        date
      });
    }

    res.status(created ? 201 : 200).json({
      success: true,
      data: {
        id: usageStat.id,
        appId: usageStat.app_id,
        childId: usageStat.child_id,
        usageTime: usageStat.usage_time,
        date: usageStat.date
      }
    });
  } catch (error) {
    next(error);
  }
};

// Batch create or update usage stats (child only)
exports.batchCreateOrUpdateUsageStats = async (req, res, next) => {
  try {
    const { stats } = req.body;

    // Check if user is a child
    if (req.user.user_type !== 'child') {
      return next(new AppError('Only children can update usage stats', 403));
    }

    // Process each stat
    const updatedStats = [];
    const notificationsData = [];

    for (const stat of stats) {
      const { appId, usageTime, date } = stat;

      // Check if child has relationship with app
      const childApp = await ChildApp.findOne({
        where: {
          child_id: req.user.id,
          app_id: appId
        },
        include: [
          {
            model: App,
            as: 'app'
          }
        ]
      });

      if (!childApp) {
        continue;
      }

      // Create or update usage stat
      const [usageStat, created] = await UsageStat.findOrCreate({
        where: {
          child_id: req.user.id,
          app_id: appId,
          date: new Date(date)
        },
        defaults: {
          usage_time: usageTime
        }
      });

      // If not created, update the usage time
      if (!created) {
        usageStat.usage_time = usageTime;
        await usageStat.save();
      }

      // Update the child app's current usage time
      childApp.usage_time = usageTime;
      await childApp.save();

      // Add to updated stats
      updatedStats.push({
        id: usageStat.id,
        appId: usageStat.app_id,
        childId: usageStat.child_id,
        usageTime: usageStat.usage_time,
        date: usageStat.date
      });

      // Add to notifications data
      notificationsData.push({
        appId,
        packageName: childApp.app.package_name,
        usageTime,
        date
      });
    }

    // Get parents of this child
    const relationships = await Relationship.findAll({
      where: { child_id: req.user.id }
    });

    // Notify parents via WebSocket
    const io = getIo();
    for (const relationship of relationships) {
      io.to(`user:${relationship.parent_id}`).emit('usage:batch-updated', {
        childId: req.user.id,
        updates: notificationsData
      });
    }

    res.status(200).json({
      success: true,
      count: updatedStats.length,
      data: updatedStats
    });
  } catch (error) {
    next(error);
  }
};
