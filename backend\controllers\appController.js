const { App } = require('../models/App');
const { ChildApp } = require('../models/ChildApp');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Get all apps
exports.getAllApps = async (req, res, next) => {
  try {
    const apps = await App.findAll();

    res.status(200).json({
      success: true,
      count: apps.length,
      data: apps
    });
  } catch (error) {
    next(error);
  }
};

// Get app by ID
exports.getAppById = async (req, res, next) => {
  try {
    const app = await App.findByPk(req.params.id);

    if (!app) {
      return next(new AppError('App not found', 404));
    }

    res.status(200).json({
      success: true,
      data: app
    });
  } catch (error) {
    next(error);
  }
};

// Create app
exports.createApp = async (req, res, next) => {
  try {
    const { packageName, appName, iconUrl } = req.body;

    // Check if app already exists
    const existingApp = await App.findOne({
      where: { package_name: packageName }
    });

    if (existingApp) {
      // If app exists, create or update child app relationship
      const [childApp, created] = await ChildApp.findOrCreate({
        where: {
          child_id: req.user.id,
          app_id: existingApp.id
        },
        defaults: {
          usage_time: 0,
          time_limit: 0,
          blocked: false
        }
      });

      return res.status(created ? 201 : 200).json({
        success: true,
        data: {
          app: existingApp,
          childApp
        }
      });
    }

    // Create new app
    const app = await App.create({
      package_name: packageName,
      app_name: appName,
      icon_url: iconUrl
    });

    // Create child app relationship
    const childApp = await ChildApp.create({
      child_id: req.user.id,
      app_id: app.id,
      usage_time: 0,
      time_limit: 0,
      blocked: false
    });

    res.status(201).json({
      success: true,
      data: {
        app,
        childApp
      }
    });
  } catch (error) {
    next(error);
  }
};

// Update app
exports.updateApp = async (req, res, next) => {
  try {
    const { appName, iconUrl } = req.body;

    // Check if app exists
    const app = await App.findByPk(req.params.id);

    if (!app) {
      return next(new AppError('App not found', 404));
    }

    // Check if child has relationship with app
    const childApp = await ChildApp.findOne({
      where: {
        child_id: req.user.id,
        app_id: app.id
      }
    });

    if (!childApp) {
      return next(new AppError('Not authorized to update this app', 403));
    }

    // Update app
    if (appName) app.app_name = appName;
    if (iconUrl !== undefined) app.icon_url = iconUrl;

    await app.save();

    res.status(200).json({
      success: true,
      data: app
    });
  } catch (error) {
    next(error);
  }
};
