const { AppError } = require('./errorHandler');

/**
 * Middleware to restrict access to specific user types
 * @param {...string} userTypes - User types allowed to access the route
 * @returns {function} Express middleware
 */
exports.restrictTo = (...userTypes) => {
  return (req, res, next) => {
    // Check if user exists and has a user_type
    if (!req.user || !req.user.user_type) {
      return next(new AppError('User not authenticated or missing user type', 401));
    }

    // Check if user type is allowed
    if (!userTypes.includes(req.user.user_type)) {
      return next(
        new AppError(
          `Access denied. User type '${req.user.user_type}' not authorized to access this resource`,
          403
        )
      );
    }

    // If user type is allowed, proceed to next middleware
    next();
  };
};

/**
 * Middleware to restrict access to specific guardian types
 * @param {...string} guardianTypes - Guardian types allowed to access the route
 * @returns {function} Express middleware
 */
exports.restrictToGuardian = (...guardianTypes) => {
  return (req, res, next) => {
    // Check if user exists and has a user_type of parent
    if (!req.user || req.user.user_type !== 'parent') {
      return next(new AppError('Only parents can access this resource', 403));
    }

    // Check if user has a guardian_type
    if (!req.user.guardian_type) {
      return next(new AppError('User does not have a guardian type', 403));
    }

    // Check if guardian type is allowed
    if (!guardianTypes.includes(req.user.guardian_type)) {
      return next(
        new AppError(
          `Access denied. Guardian type '${req.user.guardian_type}' not authorized to access this resource`,
          403
        )
      );
    }

    // If guardian type is allowed, proceed to next middleware
    next();
  };
};
