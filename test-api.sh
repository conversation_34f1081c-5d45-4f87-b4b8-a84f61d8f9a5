#!/bin/bash

# API Testing Script for smart-parttime.africa backend
BASE_URL="https://smart-parttime.africa/api"

echo "🚀 Testing Digital Wellbeing API at $BASE_URL"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local token=$4
    
    echo -e "${YELLOW}Testing: $method $endpoint${NC}"
    
    if [ -n "$token" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -H "Authorization: Bearer $token")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json")
        fi
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success ($status_code)${NC}"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ Failed ($status_code)${NC}"
        echo "$body"
    fi
    echo "---"
}

# 1. Health Check
test_endpoint "GET" "/health"

# 2. Register Parent
echo "Registering parent user..."
parent_response=$(test_endpoint "POST" "/auth/register" '{
    "username": "testparent",
    "email": "<EMAIL>",
    "password": "password123",
    "userType": "parent"
}')

# 3. Login Parent
echo "Logging in parent..."
login_response=$(curl -s -X POST "$BASE_URL/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "email": "<EMAIL>",
        "password": "password123"
    }')

# Extract token from login response
parent_token=$(echo "$login_response" | jq -r '.data.token // empty' 2>/dev/null)

if [ -n "$parent_token" ] && [ "$parent_token" != "null" ]; then
    echo -e "${GREEN}✅ Parent login successful, token obtained${NC}"
    
    # 4. Test authenticated endpoints
    test_endpoint "GET" "/auth/me" "" "$parent_token"
    test_endpoint "GET" "/users/parent/children" "" "$parent_token"
    test_endpoint "GET" "/apps" "" "$parent_token"
    
else
    echo -e "${RED}❌ Failed to get parent token${NC}"
    echo "Login response: $login_response"
fi

echo "================================================"
echo "🏁 API Testing Complete"
