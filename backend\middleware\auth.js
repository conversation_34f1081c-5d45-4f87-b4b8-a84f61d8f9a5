const jwt = require('jsonwebtoken');
const { AppError } = require('./errorHandler');
const { secret } = require('../config/jwt');
const { User } = require('../models/User');
const { AuthToken } = require('../models/AuthToken');
const { Op } = require('sequelize');

// Middleware to protect routes
const protect = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    let token;
    
    if (authHeader && authHeader.startsWith('Bearer')) {
      token = authHeader.split(' ')[1];
    }

    // Check if token exists
    if (!token) {
      return next(new AppError('Not authorized to access this route', 401));
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, secret);

      // Check if token exists in database and is not expired
      const authToken = await AuthToken.findOne({
        where: { 
          token,
          user_id: decoded.id,
          expires_at: { [Op.gt]: new Date() }
        }
      });

      if (!authToken) {
        return next(new AppError('Token is invalid or expired', 401));
      }

      // Get user from database
      const user = await User.findByPk(decoded.id);

      if (!user) {
        return next(new AppError('User not found', 404));
      }

      // Log user info for debugging
      console.log('User authenticated:', {
        id: user.id,
        userType: user.user_type,
        username: user.username
      });

      // Add user to request object
      req.user = user;
      next();
    } catch (error) {
      console.error('Auth error:', error);
      return next(new AppError('Not authorized to access this route', 401));
    }
  } catch (error) {
    console.error('Outer auth error:', error);
    next(error);
  }
};

// Middleware to restrict access to specific user types
const restrictTo = (...userTypes) => {
  return (req, res, next) => {
    // Check if request is from Flutter parent app
    const isFlutterApp = req.headers['x-app-type'] === 'flutter-parent' || 
                         req.headers['user-agent']?.includes('Flutter');
    
    // If it's the Flutter parent app and 'parent' is one of the allowed types, 
    // automatically grant access
    if (isFlutterApp && userTypes.includes('parent')) {
      return next();
    }
    
    console.log('User type check:', {
      userType: req.user.user_type,
      allowedTypes: userTypes,
      userId: req.user.id,
      isFlutterApp
    });
    
    // Normal check for other cases
    if (!userTypes.includes(req.user.user_type)) {
      return next(new AppError('Not authorized to access this route', 403));
    }
    
    next();
  };
};

module.exports = { protect, restrictTo };
